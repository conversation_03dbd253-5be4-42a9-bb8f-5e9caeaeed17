const { bot } = require('../lib');
const axios = require('axios');

bot(
    {
        pattern: 'pincode ?(.*)',
        fromMe: true,
        desc: 'Pincode lookup to get location details',
        type: 'misc',
    },
    async (message, match) => {
        if (!match) return await message.send('*Example :* 110001');

        // User input (Pincode)
        const pincode = match.trim();

        // API call to get location details based on pincode
        try {
            const response = await axios.get(`https://api.postalpincode.in/pincode/${pincode}`);
            const data = response.data;

            // Check if valid pincode
            if (data[0].Status === "Error") {
                return await message.send('Sorry, no data found for this pincode.');
            }

            // Extract location details
            const locationDetails = data[0].PostOffice.map(post => {
                return `*Area:* ${post.Name}\n*District:* ${post.District}\n*State:* ${post.State}\n*Country:* ${post.Country}`;
            }).join('\n\n');

            return await message.send(`Details for Pincode ${pincode}:\n\n${locationDetails}`);
        } catch (error) {
            return await message.send('Failed to retrieve data. Please try again later.');
        }
    }
);