const { bot, textMaker } = require('../lib')

bot(

	{		pattern: 'comic ?(.*)',

		fromMe: true,

		desc: 'comix text effects',

		type: 'textmaker',

	},

	async (message, match) => {

		if (!match) return await message.send(' *Give me a text* \n • *Eg* - *.comic* *Zenoxhh*')

		const effect_url =

			'https://en.ephoto360.com/create-online-3d-comic-style-text-effects-817.html'

		const { status, url } = await textMaker(effect_url, match)

		if (url) return await message.sendFromUrl(url)

	}

)