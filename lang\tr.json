{"extra": {"init_session": "[{0}] <PERSON><PERSON> o<PERSON>um başlatılıyor", "load_session": "[{0}] Oturum durumu kontrol ediliyor", "invalid_session": "[{0}] {1} GEÇERSİZ OTURUM KİMLİĞİ, TEKRAR TARAYIN!!!", "success_session": "[{0}] Oturum başarıyla doğrulandı.", "connecting": "[{0}] Bağlanılıyor...", "connected": "[{0}] Bağlandı {1}", "instance_close": "[{0}] Bağlantı kesildi", "instance_restart": "[{0}] Örnek Yeniden Başlatılıyor...", "reconnect": "[{0}] ye<PERSON><PERSON> bağlanıyor...({1})", "reconnect_after": "[{0}] 1 dakika sonra yeniden bağlan", "another_login": "[{0}] oturum başka bir cihazda açıldı.", "error_message": "```---HAT<PERSON> RAPORU---\n\n<PERSON><PERSON><PERSON><PERSON><PERSON> : {0}\nMesaj : {1}\nHata   : {2}\nJid     : {3}\nkomut : {4}\nPlatform : {5}```\n\n```-----``` *LyFE tarafından ❣ ile yapıldı* ```-----```", "deleted_message": "           <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "install_external_plugin": "[{0}] <PERSON><PERSON> yükleniyor...", "installed_external_plugin": "[{0}] <PERSON><PERSON>", "plugin_install": "[{0}] Eklentiler Yükleniyor...", "plugin_installed": "[{0}] {1} <PERSON><PERSON><PERSON><PERSON>", "plugins_installed": "[{0}] Eklentiler <PERSON>", "plugin_install_error": "[{0}] {1} y<PERSON><PERSON><PERSON><PERSON> hata <PERSON>, e<PERSON>nti si<PERSON>r", "plugin_not_found": "[{0}] eklenti {1} b<PERSON><PERSON><PERSON><PERSON> (404), eklenti siliniyor", "group_cmd": "Bu komut yalnızca grup sohbetlerinde kullanılabilir."}, "plugins": {"common": {"reply_to_message": "<PERSON>ir mesaja cevap verin", "not_admin": "Yönetici değilim.", "reply_to_image": "Bir resme cevap verin", "update": "_Ayarlar başarıyla güncellendi! Tercihleriniz kaydedildi ve şimdi etkin._"}, "menu": {"help": "```╭────────────────╮\n      ʟᴇᴠᴀɴᴛᴇʀ\n╰────────────────╯\n\n╭────────────────\n│ Önek : {0}\n│ Kullanıcı : {1}\n│ <PERSON>aman : {2}\n│ <PERSON><PERSON><PERSON> : {3}\n│ <PERSON>ri<PERSON> : {4}\n│ Sürüm : {5}\n│ Eklentiler : {6}\n│ Ram : {7}\n│ Çalışma Süresi : {8}\n│ Platform : {9}\n╰────────────────```", "menu": "```╭═══ LEVANTER ═══⊷\n┃❃╭──────────────\n┃❃│ Önek : {0}\n┃❃│ Kullanıcı : {1}\n┃❃│ Zaman : {2}\n┃❃│ Gün : {3}\n┃❃│ Tarih : {4}\n┃❃│ Sürüm : {5}\n┃❃│ Eklentiler : {6}\n┃❃│ Ram : {7}\n┃❃│ Çalışma Süresi : {8}\n┃❃│ Platform : {9}\n┃❃╰───────────────\n╰═════════════════⊷```"}, "afk": {"example": "> *AFK Kullanımı:*\n- AFK Ayarla: *afk [sebep]*\n- Örnek: *afk meşgulüm* Son görülme #lastseen önce\n- Bir mesaj göndererek otomatik olarak AFK durumu kaldırılır\n- AFK'yı Devre Dışı Bırak: *afk off*", "not_afk": "Artık AFK değilsiniz.", "desc": "AFK (Klavyeden Uzakta) durumunu ayarla"}, "alive": {"default": "Can<PERSON>ıyım\nÇalışma Süresi : #uptime", "desc": "Botun canlı durum mesajını g<PERSON>, iste<PERSON>e bağlı özel metin ile."}, "antifake": {"example": "*Antifake Durumu:* {0}\n\n> *<PERSON><PERSON><PERSON><PERSON>:*\n- *antifake list* - Ülke kodlarını görüntüle\n- *antifake !91,1* - Belirli ülke kodlarını izin ver/izin verme\n- *antifake on | off* - Antifake'i etkinleştir/devre dışı bırak", "desc": "Anti-sahte numara <PERSON> etkinleştir veya yapılandır", "not": "Listelenecek ülke kodu yok.", "status": "Antifake şimdi *{0}*.", "update": "> Antifake Güncellendi\n*İzin Verilen:* {0}\n*İzin Verilmeyen:* {1}"}, "antilink": {"desc": "Anti-link özelliğini etkinleştir veya yapılandır", "disable": "_Antilink zaten devre dışı._", "antilink_notset": "Antilink yapılandırılmamış.", "status": "Antilink şimdi *{0}*.", "info": "> Antilink Durumu: {0}\n*İzin Verilen URL'ler:* {1}\n *Eylem :* {2}", "action_invalid": "*Geçersiz eylem belirtildi.*", "action_update": "Antilink e<PERSON><PERSON>: *{0}*", "update": "> Antilink Güncellendi\n*İzin Verilen:* {0}\n*İzin Verilmeyen:* {1}", "example": "Antilink Durumu: *{0}*\n\n> <PERSON><PERSON><PERSON><PERSON> Ö<PERSON>kleri:\n- antilink info - Mevcut ayarları görüntüle\n- antilink whatsapp.com - Belirli URL'leri izin ver\n- antilink on | off - Antilink'i etkinleştir/devre dışı bırak\n- antilink action/<kick | warn | null> - Link i<PERSON><PERSON> eylem belirle"}, "antiword": {"desc": "Grup sohbetinde belirli kelimeleri filtrele", "example": "AntiWord Durumu: {0}\n> *<PERSON><PERSON><PERSON><PERSON>:*\n- antiword action/<kick | warn | null>* - <PERSON><PERSON><PERSON><PERSON> için eylem belirle\n- antiword on | off - Kelime filtrelemeyi etkinleştir/devre dışı bırak\n- setvar ANTIWORDS:word1,word2,... - Engellenen kelimeleri tanımla", "action_update": "<PERSON><PERSON><PERSON> e<PERSON><PERSON>: *{0}*", "status": "AntiWord şimdi *{0}*."}, "apk": {"desc": "APKMirror'<PERSON>n <PERSON> indir", "example": "> *<PERSON><PERSON><PERSON><PERSON>ri:*\n- apk Mixplorer\n- apk whatsapp,apkm (demet APK'ları dahil)", "no_result": "_Sorg<PERSON>uz için sonuç bulunamadı._", "apps_list": "Eşleşen Uygulamalar ({0})"}, "delete": {"desc": "Anti-silme: <PERSON><PERSON><PERSON> me<PERSON> kurtar", "example": "> *<PERSON><PERSON><PERSON><PERSON>:*\n- delete p - <PERSON>linen mesajları sohbetinize/sudo'ya gönder\n- delete g - <PERSON>linen mesajları aynı gruba gönder\n- delete off - Anti-silmeyi devre dışı bırak\n- delete <jid> - <PERSON><PERSON>n mesajları belirli bir JID'e gönder", "invalid_jid": "*Hata:* _Geçersiz JID_", "dlt_msg_jid": " _<PERSON><PERSON><PERSON>uraya gönderilecek: {0}_", "dlt_msg_disable": "Anti-silme devre dışı bırakıldı.", "dlt_msg_sudo": "_<PERSON><PERSON><PERSON> me<PERSON> sohbetinize veya sudo'ya gönderilecek._", "dlt_msg_chat": "_<PERSON><PERSON><PERSON> me<PERSON> sohbete kendisi gönderilecek._"}, "dlt": {"desc": "yanı<PERSON><PERSON>n mesajları sil"}, "fb": {"desc": "Facebook videolarını indir", "example": "", "quality": "<PERSON> <PERSON><PERSON><PERSON>", "invalid": "*Hata:* _Verilen URL için video bulunamadı._"}, "fancy": {"desc": "Verilen metinden şık metin oluş<PERSON>", "example": "> *<PERSON><PERSON><PERSON>m:*\nfancy <metin>\nfancy <yazı_tipi_numarası> (bir mesaja cevap olarak)\n\n*<PERSON><PERSON><PERSON>:*\n- fancy Merhaba\n- fancy 7 (bir mesaj<PERSON> yan<PERSON><PERSON><PERSON>)", "invalid": "*Geçersiz Yazı Tipi Numarası!*\nLütfen *1-47* arasında bir sayı girin."}, "stop": {"desc": "<PERSON><PERSON>bet<PERSON><PERSON> filtreleri sil", "example": "> *Kullanım:*\n- stop <filtre>\n- stop mer<PERSON>a", "delete": "{0} <PERSON><PERSON><PERSON>", "not_found": "_{0} filtrelerde bulunamadı._"}, "filter": {"desc": "<PERSON><PERSON><PERSON><PERSON> filt<PERSON>", "example": "> *Örnek:*\n- filter merhaba (bir metin mesajına cevap verirken)\n- filter list (Mevcut filtreleri gösterir)", "list": "> *Mevcut Filtreler:*\n{0}", "filter_add": "*{0}* filtresi eklendi"}, "forward": {"desc": "Yanıtlanan mesajı belirtilen JID(ler)e ilet", "foward": "<PERSON>j <PERSON>: {0}", "example": "Geçersiz JID!\n> *Kullanım:*\n- forward <jid>\n- forward <EMAIL>"}, "save": {"desc": "Yanıtlanan mesajı kendinize ilet", "save": "<PERSON><PERSON> ka<PERSON>!"}, "gemini": {"desc": "Google Gemini AI - İsted<PERSON><PERSON><PERSON><PERSON> her şeyi sorun!", "example": "> *Örnek :*\n- gemini merhaba\n- gemini resimde ne var(bir resme cevap verirken)", "Key": "> Gemini API Anahtarı Eksik!\nAlın: https://aistudio.google.com/app/apikey\n\n*Şöyle ayarlayın:*\nsetvar GEMINI_API_KEY = api_anahtarınız"}, "gstop": {"desc": "<PERSON><PERSON><PERSON> grup<PERSON> gfiltreleri sil", "example": "> *Kullanım:*\n- gstop <filtre>\n- gstop merhaba", "delete": "{0} <PERSON><PERSON><PERSON>", "not_found": "_{0} gfiltrelerde bulunamadı._"}, "pstop": {"desc": "<PERSON><PERSON><PERSON> gruplardaki pfilter'ı sil", "example": "> *Kullanım:*\n- pstop <filtre>\n- pstop merhaba", "delete": "{0} <PERSON><PERSON><PERSON>", "not_found": "_{0} pfilter'da bulunamadı._"}, "gfilter": {"desc": "Gruplardaki genel filtrel<PERSON>", "example": "> *Örnek :*\n- gfilter merhaba (bir metin mesajına cevap verirken)\n- gfilter list (Mevcut gfiltreleri gösterir)", "add": "*{0}* gfilter eklendi"}, "pfilter": {"desc": "<PERSON><PERSON><PERSON><PERSON> so<PERSON>de genel filtreleri y<PERSON>", "example": "> *Örnek :*\n- pfilter merhaba (bir metin mesajına cevap verirken)\n- pfilter list (Mevcut pfilter'ları gösterir)", "add": "*{0}* pfilter eklendi"}, "gpp": {"desc": "grup simgesini değ<PERSON>tir", "update": "_Grup simgesi Güncellendi_"}, "greet": {"setdesc": "Kişiselleştirilmiş bir karşılama mesajı ayarla", "setexample": "> *Örnek:* set<PERSON><PERSON>, bu bir bot. Patronum size en kısa zamanda cevap verecek.", "setupdate": "_Karş<PERSON>lama mesajı güncellendi._", "getdesc": "Kişiselleş<PERSON><PERSON><PERSON>ş karşılama mesajını al", "notsetgreet": "> Karşılama mesajı ayarlanmamış.", "deldesc": "Kişiselle<PERSON><PERSON><PERSON><PERSON><PERSON> karşılama mesajını sil", "delupdate": "Karş<PERSON>lama mesajı silindi."}, "greetings": {"welcome_desc": "<PERSON>ni ü<PERSON>lere hoş geldiniz mesajı gönder", "welcome_example": "Ho<PERSON> geldiniz şu anda {0}\n\nDaha fazla detay için: https://levanter-plugins.vercel.app/faq", "welcome_enable": "_<PERSON><PERSON> geldiniz <PERSON>_", "welcome_disable": "_Hoş geldiniz şimdi devre dışı_", "welcome_delete": "_<PERSON><PERSON> geldiniz mesajı silindi_", "goodbye_desc": "Üyelere elveda mesajı gönder", "goodbye_example": "<PERSON><PERSON><PERSON> anda {0}\n\nDaha fazla detay için: https://levanter-plugins.vercel.app/faq", "goodbye_enable": "_<PERSON><PERSON><PERSON>_", "goodbye_disable": "_<PERSON><PERSON><PERSON> dışı_", "goodbye_delete": "_<PERSON><PERSON><PERSON> mesajı silind<PERSON>_"}, "groq": {"example": "*Örnek:* groq Merhaba\n\nİsteğe bağlı olarak aşağıdaki ortam değişkenlerini ayarlayabilirsiniz:\n- GROQ_API_KEY\n- GROQ_MODEL\n- GROQ_SYSTEM_MSG\n\nDaha fazla detay için: https://console.groq.com/keys", "desc": "GROQ AI ile etkileşim kur"}, "kick": {"desc": "Üyeleri Gruptan <PERSON>.", "not_admin": "Yönetici değilim, üyeleri çıkaramam.", "mention_user": "Lütfen bir kullanıcıyı etiketleyin veya mesajlarına cevap verin.", "admin": "Belirtilen kullanıcı yönetici olduğu için çı<PERSON>ı<PERSON>.", "kicking_all": "Yönetici olmayan tüm üyeler çıkarılıyor... ({0} üye). Botu durdurmak için yeniden başlatın."}, "add": {"desc": "üyeleri gruplara ekle", "warning": "> Kişileri kontaklarınıza kaydetmeden eklemek riski artırabilir.", "not_admin": "Yönetici <PERSON>, üyeleri ekleyemem.", "invalid_number": "Lütfen geçerli bir telefon numarası girin. Örnek: add 91987654321", "failed": "Ekleme başarısız. Bir davetiye gönderildi."}, "promote": {"desc": "Yönetici rolü ver", "not_admin": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>.", "mention_user": "Lütfen bir kullanıcıyı etiketleyin veya mesajlarına cevap verin.", "already_admin": "Kullanıcı zaten yönetici."}, "demote": {"desc": "Yönetici rolünü kaldır", "not_admin": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>.", "mention_user": "Lütfen bir kullanıcıyı etiketleyin veya mesajlarına cevap verin.", "not_admin_user": "Kullanıcı yönetici değil."}, "invite": {"desc": "Grup davet bağlantısı al", "not_admin": "Yönetici değilim, davet bağlantısı oluşturamam.", "success": "İşte grup davet bağlantısı:\n{0}"}, "mute": {"desc": "Grupları yalnızca yöneticiler yap", "not_admin": "Yönetici değilim, grup ayarlarını değiştiremem.", "mute": "{0} da<PERSON><PERSON> s<PERSON> su<PERSON>."}, "unmute": {"desc": "Grup tüm katılımcıların mesaj göndermesine izin ver", "not_admin": "Yönetici değilim, grup ayarlarını değiştiremem."}, "join": {"desc": "Davet bağlantısı ile gruplara katıl", "invalid_link": "Lütfen geçerli bir WhatsApp grup davet bağlantısı girin.", "group_full": "Grup dolu ve yeni üyeleri kabul edemiyor.", "success": "Gruba başarıyla katıldı.", "request_sent": "<PERSON><PERSON><PERSON>."}, "revoke": {"desc": "Grup davet bağlantısını iptal et", "not_admin": "Yönetici <PERSON>, davet ba<PERSON>lantısını iptal edemem."}, "group_info": {"desc": "Grup davet bağlantısı bilgisini göster", "invalid_link": "Lütfen geçerli bir WhatsApp davet bağlantısı girin.", "details": "*İsim:* {0}\n*Grup Kimliği:* {1}@g.us\n*Sahip:* {2}\n*Üyeler:* {3}\n*Oluşturulma tarihi:* {4}\n*Açıklama:* {5}"}, "common_members": {"desc": "İki veya daha fazla grubun ortak üyelerini göster veya <PERSON>", "found": "0 ortak üye bulundu."}, "insta": {"usage": "Örnek: insta <Instagram URL>", "not_found": "Bulunamadı.", "desc": "Instagram gönderilerini, reels'leri ve videolarını indir"}, "ison": {"usage": "Örnek: ison <telefon numarası>", "not_exist": "`*Whatsapp'ta Yok* ({0})\n`", "exist": "\n*Whatsapp'ta Var* ({0})\n", "privacy": "*Gizlilik Ayarları Açık* ({0})\n", "desc": "Bir telefon numarasının WhatsApp'ta kayıtlı olup olmadığını kontrol et"}, "lydia": {"usage": "Kullanım: lydia on | off\nBir kullanıcıya cevap vererek veya etiketleyerek etkinleştirin.", "activated": "<PERSON>.", "deactivated": "Lydia devre dışı bırakıldı.", "note": "Bu yalnızca bir mesajı yanıtlayarak çalışır.", "desc": "So<PERSON>bet botu özelliğini etkinleştir veya devre dışı bırak"}, "rotate": {"usage": "Örnek: rotate right|left|flip (bir <PERSON><PERSON> cevap o<PERSON>).", "not_found": "Lütfen bir videoya cevap verin ve geçerli bir döndürme yön<PERSON> beli<PERSON>in (sağ, sol veya çevir).", "desc": "B<PERSON>yu sağa, sola döndür veya çevir", "convert": "_Dönüştürülüyor..._"}, "mp3": {"usage": "Bir video veya ses dosyasına cevap vererek MP3'e dönüştürün.", "not_found": "Lütfen bir video veya ses mesajına cevap verin.", "desc": "Bir videoyu ses dosyasına veya ses parçasını sesli nota dönüştür"}, "photo": {"usage": "Bir fotoğ<PERSON><PERSON> cevap vererek bir resme dö<PERSON>üş<PERSON>ürün.", "desc": "Bir çıkartmayı bir resme dönüştür"}, "reverse": {"usage": "Bir video veya ses dosyasına cevap vererek oynatmayı tersine çevirin.", "not_found": "Lütfen bir video veya ses mesajına cevap verin.", "desc": "Bir videonun veya ses parçasının oynatımını tersine çevir"}, "cut": {"usage": "Örnek: cut 0;30 (b<PERSON><PERSON><PERSON><PERSON><PERSON>;s<PERSON><PERSON>) (bir video veya ses dosyasına cevap olarak).", "not_found": "Lütfen geçerli başlangıç ve süre değ<PERSON>iyle (örn. 10;30) bir video veya ses dosyasına cevap verin.", "desc": "Bir ses veya video dosyasından bir bölümü kes"}, "trim": {"usage": "Örnek: trim 10;30 (bir <PERSON><PERSON> c<PERSON> o<PERSON>).", "not_found": "Lütfen geçerli başlangıç ve süre değerleriyle (örn. 60;30) bir videoya cevap verin.", "desc": "Belirtilen başlangıç ve süre arasında bir video kırp"}, "page": {"usage": "Örnek: page 1 (bir resme ceva<PERSON> o<PERSON>).", "not_found": "Lütfen sayfa numarasını belirten sayısal bir altyazı ile bir resme cevap verin.", "desc": "Bir resmi PDF belgesine sayfa olarak ekle", "add": "Sayfa {0} eklendi!"}, "pdf": {"usage": "Örnek: pdf note (PDF için bir başlık belirtin).", "not_found": "Lütfen PDF belgesi için bir başlık belirtin.", "desc": "Resimleri PDF belgesine dönüştür"}, "merge": {"usage": "Örnek: merge 1 (bir videoya cevap verirken bir sipariş numarası ile).", "not_found": "Lütfen geçerli bir sipariş numarasıyla bir videoya cevap verin.", "desc": "<PERSON>en fazla videoyu tek bir videoya birleştir", "merge": "_{0} video birleştiriliyor_", "add": "_Video {0} eklendi_"}, "compress": {"usage": "Bir videoya cevap vererek sıkıştırın.", "desc": "Bir video dosyasını sıkıştırıp boyutunu küçült"}, "bass": {"usage": "<PERSON><PERSON><PERSON>: bass 10 (bir ses veya <PERSON>ya ceva<PERSON>).", "desc": "Bir ses dosyasının bas seviyesini değiştir"}, "treble": {"usage": "Örnek: treble 10 (bir ses veya videoya cevap o<PERSON>).", "desc": "Bir ses dosyasının tiz ses seviyesini değiştir"}, "histo": {"usage": "Bir ses veya videoya cevap vererek bir histogram video oluşturun.", "desc": "Ses dosyasını görsel bir histogram videoya dönüştür"}, "vector": {"usage": "Bir ses veya videoya cevap vererek bir vektör görselleştirme videosu oluşturun.", "desc": "Ses dosyasını bir vektör görselleştirme videosuna dönüştür"}, "crop": {"usage": "Örnek: crop 512,512,0,512 (bir <PERSON>ya cevap olarak).", "not_found": "Lütfen geçerli kırpma boyutlarıyla (out_w,out_h,x,y formatında) bir videoya cevap verin.", "desc": "Bir <PERSON>yu belirtilen boyutlara kırp", "xcrop": "Video genişliği: *{0}*, <PERSON><PERSON>kseklik: *{1}*\nAralıkta çıktı boyutu seçin."}, "low": {"usage": "Bir ses veya videoya cevap vererek ses tonunu dü<PERSON>.", "desc": "Bir ses dosyasının ses tonunu daha düşük bir tona değiştir"}, "pitch": {"usage": "Bir ses veya videoya cevap vererek ses tonunu a<PERSON>ın.", "not_found": "Lütfen bir ses veya video mesajına cevap verin.", "desc": "<PERSON>ir ses dosyasının ses tonunu ayarla"}, "avec": {"usage": "Bir ses veya videoya cevap vererek bir video formata dönüştürün.", "not_found": "Lütfen bir ses veya video mesajına cevap verin.", "desc": "Bir ses parçasını bir videoya dönüştür"}, "avm": {"usage": "Bir ses ve videoyu birleştirmek için ikisine de cevap verin.", "desc": "Ses ve video dosyalarını tek bir dosyaya birleştir", "audio_add": "_<PERSON><PERSON> e<PERSON>!_", "video_add": "_Video eklendi!_"}, "black": {"usage": "Bir ses veya videoya cevap vererek siyah arka planlı bir video oluşturun.", "desc": "Bir ses parçasını siyah arka planlı bir videoya dönüştür"}, "mediafire": {"usage": "Örnek: mediafire <Mediafire URL>", "not_found": "Dosya bulunamadı. Lütfen URL'yi doğrulayın ve tekrar deneyin.", "desc": "Mediafire'dan bir dosya indir"}, "mention": {"usage": "Örnek: mention on | off | get\n(Bir mesaja cevap vererek belirli bir kullanıcıyı hedefleyin.)", "desc": "Otomatik cevaplar için bahsetme özelliğini yapılandırın ve yönetin", "not_activated": "<PERSON><PERSON><PERSON> verme b<PERSON> etkinleştirilmedi.", "current_status": "Bahsetme {0}. Daha fazla detay için: https://levanter-plugins.vercel.app/faq", "activated": "<PERSON><PERSON><PERSON> verme bah<PERSON><PERSON> et<PERSON>.", "deactivated": "Cevap verme bahsetmesi devre dışı bırakıldı.", "updated": "Bahsetme ayarları güncellendi."}, "status": {"usage": "Kullanım: status on | off | no-dl | except-view <jid,...> | only-view <jid,...>", "desc": "WhatsApp durumlarının otomatik olarak görüntülenmesini yönet"}, "call": {"usage": "Kullanım: call on | off", "desc": "Gelen aramaları otomatik olarak reddet"}, "read": {"usage": "Kullanım: read on | off", "desc": "Gelen mesajların otomatik okunmasını etkinleştir veya devre dışı bırak"}, "online": {"usage": "Kullanım: online on | off", "desc": "<PERSON><PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> durumunu her zaman çevrimiçi tut"}, "movie": {"usage": "Örnek: movie <film adı>", "not_found": "Film bulunamadı. Lütfen başlığı doğrulayın ve tekrar deneyin.", "desc": "OMDB API'sinden tam ayrıntılı film bilgilerini al"}, "msgs": {"desc": "Her üyenin mesaj say<PERSON>, toplam mesajları ve son gö<PERSON><PERSON><PERSON><PERSON> süresini göster", "msg_init": "\n*Numara :* {0}\n*İsim :* {1}\n*Toplam Mesajlar :* {2}\n", "msg_last": "*son<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> :* {0} önce\n"}, "reset": {"usage": "Örnek: reset all OR reset <yanıt/bahsetme>", "desc": "Tüm grubun veya belirli bir üyenin mesaj sayısını sıfırla", "reset_all": "<PERSON><PERSON><PERSON> mesaj <PERSON> silindi", "reset_one": "_@{0} mesaj <PERSON> si<PERSON>._"}, "inactive": {"usage": "> Örnekler:\n-inactive day 10\n- inactive day 10 kick\n- inactive total 100\n- inactive total 100 kick\n- inactive day 7 total 150\n- inactive day 7 total 150 kick\n\n'kick' be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sadece listele", "desc": "<PERSON><PERSON> sayısı veya etkin olmayan süreye göre etkin olmayan üyeleri tanımlayın veya çıkarın. 'kick' ekleyerek üyeleri çıkarın.", "inactives": "_<PERSON>lam etkin olmayanlar : {0}_", "removing": "_7 saniye içinde {0} etkin olmayan üye çıkarılıyor_"}, "amute": {"usage": "Kullanım: amute <saat> <dakika>\n- amute on | off\n- amute info\n\nSusturma mesajını ayarlamak için bir metne cevap verin", "desc": "Belirli bir zamanda otomatik olarak grupları susturmak için z<PERSON>layın, isteğe bağlı özel mesaj ile", "not_found": "AutoMute ayarları bulunamadı.", "already_disabled": "AutoMute zaten devre dışı.", "enabled": "AutoMute Etkinleştirildi.", "disabled": "AutoMute Devre Dışı Bırakıldı.", "invalid_format": "> Örnek:\n- amute 6 0\n- amute on | off\n- amute info\n\nSusturma mesajını ayarlamak için bir metne cevap verin.", "scheduled": "Grup {0}'da su<PERSON><PERSON><PERSON><PERSON><PERSON>\n*mesaj :* {1}", "info": "Saat: {0}\nDakika: {1}\nZaman: {2}\nSustur: {3}\nMesaj: {4}"}, "aunmute": {"usage": "Kullanım: aunmute <saat> <dakika>\n- aunmute on | off\n- aunmute info\nSusturma mesajını ayarlamak için bir metne cevap verin", "desc": "Belirli bir zamanda otomatik olarak grupları susturmayı kaldırmak için zamanlayın, isteğe bağlı özel mesaj ile", "not_found": "AutoUnMute ayarları bulunamadı.", "already_disabled": "AutoUnMute zaten devre dışı.", "enabled": "AutoUnMute Etkinleştirildi.", "disabled": "AutoUnMute Devre Dışı Bırakıldı.", "invalid_format": "> Örnek:\n- aunmute 16 30\n- aunmute on | off\n- aunmute info\n\nSusturma mesajını ayarlamak için bir metne cevap verin.", "scheduled": "Grup {0}'da susturma kaldırılacak\n*mesaj :* {1}", "info": "Saat: {0}\nDakika: {1}\nZaman: {2}\nSustur: {3}\nMesaj: {4}"}, "zushi": {"usage": "> Örnek:\n- zushi ping, sticker\n- <PERSON>ü<PERSON> komutları etkinleştirmek için 'list' yazın ve ardından kopyalanan mesaja cevap verin (örn. zushi copied_message).", "desc": "<PERSON><PERSON><PERSON><PERSON> başkalarının kullanmasına izin vermek için belirli komutları etkinleştir", "already_set": "{0} zaten yapılandırıldı.", "allowed": "*izin verilen komutlar* @{0}\n{1}"}, "yami": {"usage": "<PERSON><PERSON><PERSON> yazın: yami", "desc": "<PERSON><PERSON> anda bu sohbette izin verilen komutların listesini g<PERSON>", "not_set": "Henüz izin verilen komut ayarlanmadı."}, "ope": {"usage": "Örnek: ope ping, sticker OR ope all", "desc": "Belirtilen izin verilen komutları sil veya iptal et", "not_found": "{0} i<PERSON><PERSON> izin verilen komut bulunamadı.", "all_removed": "Tüm izin verilen komutlar kaldırıldı.", "removed": "*silinen komutlar* @{0}\n{1}"}, "pdm": {"usage": "Kullanım: pdm on | off", "desc": "Grup yönetici/yönetici olmayan etkinlikleri için otomatik bildirimleri etkinleştir veya devre dışı bırak", "not_found": "<PERSON><PERSON><PERSON><PERSON> 'on' veya 'off' belirtin. Örnek: pdm on", "activated": "Yönetici/yönetici olmayan uyarı etkinleştirildi.", "deactivated": "Yönetici/yönetici olmayan uyarı devre dışı bırakıldı."}, "ping": {"desc": "Botun yanıt sü<PERSON>ini (gecikme) kontrol et", "ping_sent": "Ping!", "pong": "Pong! Yanıt süresi: {0} ms"}, "pinterest": {"usage": "Örnek: pinterest <Pinterest URL>", "not_found": "Medya bulunamadı. Lütfen URL'yi kontrol edin ve tekrar deneyin.", "desc": "Pinterest'ten videoları veya resimleri indir"}, "plugin": {"usage": "> Örnek:\n- plugin <Gist URL>\n- plugin list", "desc": "Harici eklentileri Gist URL'sini sağlayarak yükleyin veya tüm yüklü eklentileri listeleyin", "invalid": "Lütfen geçerli bir eklenti URL'si veya eklenti adı girin.", "not_installed": "<PERSON><PERSON> anda hi<PERSON>bir eklenti yüklü <PERSON>ğ<PERSON>.", "installed": "<PERSON><PERSON> e<PERSON>: {0}"}, "remove": {"usage": "> Örnek:\b- remove <plugin_name>\n- remove all", "desc": "Eklenti adını belirterek harici eklentileri silin veya tüm yüklü eklentileri kaldırın", "not_found": "Eklenti *{0}* bulunamadı.", "removed": "Eklentiler başarıyla kaldırıldı."}, "reboot": {"desc": "PM2 kullanarak bot örneğini yeniden başlat", "starting": "Yeniden başlatılıyor..."}, "fullpp": {"usage": "Örnek: fullpp (bir resme cevap o<PERSON>)", "desc": "<PERSON> profil resmi a<PERSON>", "updated": "Profil re<PERSON><PERSON>."}, "jid": {"desc": "Bir kullanıcının veya sohbetin JID'sini döndürür. Bir kullanıcıyı bah<PERSON>er, bir mesaja cevap verir veya mevcut sohbetin JID'sini varsayılan olarak kullanır."}, "left": {"desc": "Mevcut grubu terk eder. Ek me<PERSON>, çıkmadan önce gönderilir."}, "block": {"usage": "Örnek: block (bir kullanıcıyı bahsederek veya mesajına cevap vererek)", "desc": "Belirtilen kullanıcıyı engelle", "status": "<PERSON>gel<PERSON>di"}, "unblock": {"usage": "Örnek: unblock (bir kullanıcıyı bahsederek veya mesajına cevap vererek)", "desc": "Belirt<PERSON>n kull<PERSON>ı<PERSON>ının engelini kaldır", "status": "_En<PERSON><PERSON> kaldı<PERSON>_"}, "pp": {"usage": "Örnek: pp (bir resme cevap o<PERSON>)", "desc": "<PERSON><PERSON><PERSON><PERSON><PERSON> mesajdaki resmi kull<PERSON> profil resm<PERSON>zi g<PERSON>n"}, "whois": {"number": "*Numara :* {0}", "name": "*İsim :* {0}", "about": "*Hakkında :* {0}", "setAt": "*setAt :* {0}", "owner": "*Sahip :* {0}", "members": "*Üyeler* : {0}", "description": "*açıklama* : {0}", "created": "*Oluşturuldu* : {0}", "usage": "Örnek: whois <jid veya kullanıcı tanımlayıcı>", "desc": "Bir kullanıcının veya grubun profil resmini ve ek bilgilerini (hakkında, durum vb.) göster"}, "gjid": {"desc": "Tüm grup JID'lerini ve grup adlarını listele"}, "qr": {"usage": "Örnek: qr test  VEYA  bir QR resmine qr ile cevap verin", "desc": "Sağlanan metinden bir QR kodu oluşturun veya bir QR resmine cevap olarak QR kodunu çözün"}, "reddit": {"usage": "Örnek: reddit <URL>", "desc": "Belirtilen URL'den Reddit gönderisinden video indir", "error": "Verilen URL için video bulunamadı."}, "rmbg": {"usage": "Örnek: rmbg (bir resme cevap o<PERSON>)", "desc": "Yanıtlanan resmin arka planını remove.bg API kullanarak kaldır", "key": "Bu komutu kullanmak için remove.bg'de kaydolmanız, hesabınızı doğrulamanız, API anahtarınızı kopyalamanız ve .setvar RMBG_KEY:<api_anahtarınız> (örn. .setvar RMBG_KEY:GWQ6jVy9MBpfYF9SnyG8jz8P) ile ayarlamanız gerekir. KAYIT: https://accounts.kaleido.ai/users/sign_up | API ANAHTARI: https://www.remove.bg/dashboard#api-key", "error": "Eksik API anahtarı veya resim. Lütfen API anahtarınızı ayarlayın ve bir resme cevap verin."}, "setschedule": {"usage": "> *Örnek :*\n- setschedule jid,min-hour-day-month (24 saat formatında, gün ve ay isteğe bağlı)\n- setschedule <EMAIL>, 9-9-13-8\n- setschedule <EMAIL>, 0-10 (her gün saat 10'da mesaj gönder)\n- setschedule <EMAIL>, 0-10, once (saat 10'da bir kez mesaj gönder)", "desc": "Belirli bir zamanda otomatik olarak mesaj göndermek için zamanlayın. Hedef JID(ler)i ve zamanı (dakika-saat-gün-ay formatında; gün ve ay isteğe bağlı) sağlayın. Göndermek istediğiniz mesaja cevap verin.", "invalid": "Geçersiz zamanlama formatı veya zaman. Lütfen verilen örneklerden birini izleyin.", "no_reply": "Lütfen göndermek istediğiniz mesaja cevap verin.", "scheduled": "_Başarıyla {0}'de @{1}'e göndermek için zamanlandı._"}, "getschedule": {"desc": "Belirtilen sohbet için tüm zamanlanmış mesajları al", "not_found": "Zamanlanmış mesaj yok.", "time": "Zaman : {0}"}, "delschedule": {"usage": "> Örnek:\n- delschedule <EMAIL>, 8-8-10-10\n- delschedule <EMAIL>\n- delschedule all", "desc": "Hedef JID ve zamanı belirterek zamanlanmış bir mesajı silin veya tüm zamanlanmış mesajları kaldırın", "invalid": "Geçersiz format. Lütfen örneklerden birini izleyin.", "not_found": "Zamanlama bulunamadı.", "deleted": "_<PERSON><PERSON><PERSON><PERSON> silind<PERSON>._"}, "setstatus": {"usage": "Örnek: setstatus jid,jid,jid,... VEYA setstatus contact", "desc": "Bel<PERSON>li kişilere veya içe aktarılan kişilere WhatsApp durumu ayarlayın. <PERSON><PERSON><PERSON> ayarlamak için bir mesaja (metin, resim veya video) cevap verin.", "reply_required": "Lütfen durumu a<PERSON> i<PERSON>in bir mesaja cevap verin.", "sent": "_Durum {0} kişiye gönderildi._"}, "scstatus": {"usage": "Örnekler:\n- scstatus jid,jid,jid,...|min-hour-day-month (gün ve ay isteğe bağlı)\n- scstatus contact|min-hour-day-month\n- scstatus delete all|min-hour-day-month\n- scstatus list", "desc": "Belirli bir zamanda WhatsApp durumunu göndermek için zamanlayın. 'contact' i<PERSON>in içe aktarılan kişileri veya belirli JID'leri kullanın.", "reply_required": "Lütfen durumu z<PERSON> için bir mesaja cevap verin.", "invalid": "Geçersiz format. Lütfen verilen örneklerden birini izleyin.", "scheduled": "_Başarıyla {0}'de göndermek için zamanlandı._", "list": "Zamanlanmış durum listesi:", "deleted": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>."}, "antispam": {"usage": "Kullanım: antispam on | off", "desc": "Grup için AntiSpam özelliğini etkinleştir veya devre dışı bırak", "activated": "AntiSpam etkinleştirildi.", "deactivated": "AntiSpam devre dışı bırakıldı."}, "sticker": {"desc": "Bir resmi veya videoyu çıkartmaya dönüştür. Bir resme veya video mesajına cevap vererek çıkartma oluşturun.", "reply_required": "Lütfen bir resme veya videoya cevap verin."}, "circle": {"desc": "Bir resmi dairesel bir çıkartmaya dönüştür", "reply_required": "Lütfen bir resme cevap verin."}, "take": {"usage": "Örnek: take <ba<PERSON><PERSON><PERSON><PERSON>,sanat<PERSON><PERSON>lar,url> (bir <PERSON><PERSON>kartmaya veya ses dosyasına cevap olarak). Ses i<PERSON>in başlık gerekli; sanatçılar ve URL isteğe bağlı.", "desc": "Çıkartma paketini güncelleyerek meta verilerini değiştir. Bir çıkartmaya cevap verirken paket meta verilerini güncelleyin. Ses dosyasına cevap verirken dosyaya meta veri (başlık, sanatçılar, URL) ekleyin.", "reply_required": "Lütfen bir çıkartmaya veya ses mesajına cevap verin.", "additional_info": "Ses meta verileri için sanatçılar veya URL isteğe bağlıdır."}, "mp4": {"desc": "Animasyonlu bir <PERSON><PERSON><PERSON> (WebP) dosyasını bir MP4 video dosyasına dönüştür", "reply_required": "Lütfen animasyonlu bir çıkartmaya cevap verin."}, "story": {"usage": "Örnek: story <kullanıcı adı> (veya bir mesajda kullanıcı adını içeren bir mesaja cevap olarak)", "desc": "Belirtilen kullanıcı adı için Instagram hikayelerini indir. Birden fazla hikaye varsa, se<PERSON><PERSON> ya<PERSON>ak için bir liste sağlanır.", "not_found": "Sağlanan kullanıcı adı için hikaye bulunamadı.", "list": "Toplam {0} hikaye mevcut. Lütfen indirmek istediğiniz hikayeyi seçin.\n"}, "tag": {"usage": "Örnek: tag all | tag admin | tag notadmin | tag <özel mesaj> (veya bir mesaja 'tag' ile cevap o<PERSON>)", "desc": "Gruptaki üyeleri seçiminize göre etiketle. 'all' tüm üyeleri, 'admin' ya<PERSON><PERSON><PERSON><PERSON> yö<PERSON>ici<PERSON>, 'notadmin' yönetici olmayan üyeleri etiketler. Özel bir mesaj ekleyerek etiketleme yapın."}, "tictactoe": {"usage": "Örnek: tictactoe <rakip_jid> VEYA tictactoe restart <rakip_jid> VEYA tictactoe end", "desc": "Bir rakiple XOX oyunu oyna. <PERSON>ir kullanıcıyı bah<PERSON>erek, mesajına cevap vererek veya JID'yi belirterek meydan okuyun. 'tictactoe end' oyunu bitirir ve 'tictactoe restart <rakip_jid>' yeni bir rakiple oyunu yeniden başlatır.", "choose_opponent": "Lütfen bir raki<PERSON>, bir mesaja cevap vererek veya bir kullanıcıyı bahsederek. Kendinizle oynayamazsınız.", "game_ended": "<PERSON><PERSON> bitti.", "game_restarted": "Yeni bir rakiple oyun yeniden başlatıldı.", "invalid_input": "Geçersiz giriş. Lütfen geçerli bir rakip belirtin veya 'end' veya 'restart' kullanın.", "players": "Oyuncular", "already_occupied": "_<PERSON><PERSON><PERSON>_", "current_player": "geçerliOyuncu", "game_finish": "Oyun Bitti 🏁", "winner": "<PERSON><PERSON>"}, "tiktok": {"usage": "Kullanım: tiktok <TikTok URL> (veya bir mesaja URL ile cevap olarak)", "desc": "Sağlanan URL'den TikTok video indir", "not_found": "Video bulunamadı. Lütfen URL'yi kontrol edin ve tekrar deneyin."}, "tog": {"usage": "Örnek: tog ping off", "desc": "Bir bot komutunu etkinleştir veya devre dışı bırak", "invalid": "Geçersiz giriş. Kullanım: tog <komut> on|off (örn. tog ping off)", "self_reference": "<PERSON><PERSON><PERSON> gerçekten böyle davranmak istedin mi?", "enabled": "{0} Etkinleştirildi.", "disabled": "{0} Devre Dışı Bırakıldı."}, "trt": {"usage": "Örnek: trt ml hi VEYA trt ml (bir metin mesajına cevap olar<PERSON>)", "desc": "Google Çeviri kull<PERSON>rak metin çev<PERSON>. <PERSON><PERSON><PERSON> dil kodunu (ve isteğe bağlı olarak kaynak dil kodunu) belirterek bir mesaja cevap verin."}, "twitter": {"usage": "Örnek: twitter <Twitter URL> (veya bir mesaja URL içeren cevap olarak)", "desc": "Twitter'dan video indir. <PERSON>en fazla kalite se<PERSON>, seç<PERSON> ya<PERSON> isten<PERSON>.", "not_found": "Sağlanan Twitter URL'si için video bulunamadı.", "choose_quality": "> Video <PERSON><PERSON><PERSON>\n"}, "upload": {"usage": "Örnek: upload <URL> (veya bir mesaja URL içeren cevap olarak)", "desc": "Sağlanan URL'den medya indir. Kısaltılmış Google Resimleri URL'leri için doğrudan resim URL'si otomatik olarak alınır."}, "url": {"usage": "Örnek: url VEYA url imgur (bir resme veya videoya cevap olarak)", "desc": "Bir resmi veya videoyu URL'ye dönüştür. İsteğe bağlı olarak bir parametre (örn. 'imgur') belirterek belirli bir hizmetten URL alın."}, "getvar": {"usage": "Örnek: <PERSON><PERSON> sudo", "desc": "Bir değişkenin değerini göster. Değerini almak için değişken anahtarını (büyük/küçük harf duyarlı değil) sağlayın.", "not_found": "{0} değişkenlerde bulunamadı."}, "delvar": {"usage": "Örnek: <PERSON><PERSON> sudo", "desc": "Bir değişkeni anahtarını belirterek sil", "not_found": "{0} değişkenlerde bulunamadı.", "deleted": "{0} <PERSON><PERSON><PERSON>."}, "setvar": {"usage": "Örnek: setvar key = value", "desc": "Belirli bir anahtar ve değerle bir değişken ayarla. Anahtarı değerden '=' ile ayırın.", "success": "<PERSON><PERSON> var {0} olar<PERSON> {1} eklendi."}, "allvar": {"desc": "T<PERSON>m saklanan değişkenleri sıralı olarak göster"}, "vote": {"usage": "> Örnek :\nvote q|<PERSON>zin en sevdiğiniz renk nedir?\no|😀|Mavi\no|😊|Kırmızı", "desc": "Bir WhatsApp grubunda oylama ba<PERSON>lat", "notes": "Alıcı be<PERSON>, oylama mesajı mevcut gruba gönderilir.", "no_vote": "O<PERSON>ma yok!", "total_vote": "toplam oylar : *{0}*", "delete_vote": "<PERSON>ni bir oylama ayarlamak için mevcut oylamayı silin.", "option_required": "iki veya daha fazla seçenek gerekli", "question_required": "soru gerekli", "vote": "Tepki vererek veya seçeneğe cevap vererek oy verin.", "vote_deleted": "_<PERSON><PERSON><PERSON> si<PERSON>._", "voted": "@{0} {1} için oy kullandı\n\n${2}"}, "warn": {"usage": "Örnek: warn @user VEYA warn reset @user. Bir kullanıcının mesajına cevap vererek 'warn' veya 'warn reset' yazabilirsiniz.", "desc": "Grup sohbetinde bir kullanıcıyı uyar. Bu komut bir kullanıcının uyarı sayısını artırır. Uyarı sayısı sınırı aşılırsa, kullanıcı grubdan atılır. 'warn reset' kullanarak bir kullanıcının uyarılarını sıfırla.", "reset_usage": "Örnek: warn reset @user (veya bir kullanıcının mesajına 'warn reset' yazarak)", "cannot_remove_admin": "Bir yöneticiyi kaldıramam."}, "wcg": {"usage": "Örnekler:\n- wcg start (kelime <PERSON> oyununu z<PERSON>la b<PERSON>)\n- wcg end (mevcut oyunu bitir)\n- wcg <kelime> (kelime zinc<PERSON> de<PERSON>m ettirerek oyna)", "desc": "<PERSON><PERSON><PERSON> Zinciri O<PERSON>u: <PERSON><PERSON><PERSON><PERSON> devam ettirerek oyuna katılın. 'wcg start' yeni bir oyun başlatır veya 'wcg end' mevcut oyunu bitirir."}, "wrg": {"usage": "Örnekler:\n- wrg start (rastgele kelime oyununu z<PERSON> b<PERSON>)\n- wrg end (mevcut oyunu bitir)\n- wrg <kelime> (rastgele bir kelime göndererek oyna)", "desc": "Ra<PERSON><PERSON><PERSON>: Rast<PERSON><PERSON> bir kelimeye cevap vererek oyuna katılın. 'wrg start' yeni bir oyun başlatır ve 'wrg end' mevcut oyunu bitirir."}, "yts": {"usage": "Örnek: yts baymax", "desc": "Sorgu veya URL ile YouTube videolarını ara. Geçerli bir YouTube URL'si sağlanırsa, ilk videonun ayrıntılı bilgileri döndürülür."}, "song": {"usage": "<PERSON><PERSON><PERSON>: song indila love story VEYA song <YouTube URL> (bir mesaja cevap olarak da kullanılabilir)", "desc": "YouTube'dan bir şarkı indir. Bir URL sağlanırsa şarkı doğrudan indirilir; aksi halde bir arama yapılır ve sonuçlar listelenir.", "not_found": "Sağlanan sorgu veya URL için şarkı bulunamadı."}, "video": {"usage": "<PERSON><PERSON><PERSON>: video <YouTube URL> (veya bir mesaja URL ile cevap olarak)", "desc": "YouTube'dan bir video indir. Doğrudan bir URL sağlanırsa video indirilir; bir arama sorgusu sağlanırsa sonuçlar listelenir.", "not_found": "Sağlanan sorgu veya URL için video bulunamadı."}, "update": {"usage": "Örnek: update", "desc": "Yeni güncellemeleri kontrol et. Mevcut güncellemeleri gösterir veya botun güncel olduğunu doğrular.", "up_to_date": "<PERSON><PERSON> zaten güncel.", "available": "{0} yeni g<PERSON>me mevcut:\n{1}"}, "update_now": {"usage": "Örnek: update now", "desc": "<PERSON><PERSON> en son s<PERSON><PERSON><PERSON><PERSON>", "up_to_date": "<PERSON><PERSON> zaten güncel. <PERSON><PERSON> güncelleme yok.", "updating": "Bot güncelleniyor...", "updated": "Bot başarıyla güncellendi!"}, "antigm": {"usage": "> Usage:\n- antigm <on|off> — Enable or disable anti group mention\n- antigm <delete|warn|kick> — Set action when someone mentions the group\n- antigm ignore <jid,jid,jid,...> — Ignore anti group mention from specific groups", "action": "Action updated: group mentions will now result in *{0}*.", "filter": "Ignore JIDs updated for anti group mention.", "enabled": "Anti group mention has been enabled.", "disabled": "Anti group mention has been disabled."}}}