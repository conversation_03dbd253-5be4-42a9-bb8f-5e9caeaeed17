# 🎬 Enhanced YouTube Downloader Plugin

## 📋 What This Plugin Does

This comprehensive YouTube downloader plugin provides:

- **🎬 Video Downloads** with multiple quality options (720p, 480p, 360p, etc.)
- **🎵 Audio Downloads** in MP3 format with metadata
- **🔍 Smart Search** functionality with interactive selection
- **⚡ Auto Download** with "auto" keyword for quick downloads
- **🔄 Multiple Fallbacks** for maximum reliability
- **🍪 Cookie Support** for better YouTube access

## 🚀 Installation & Setup

### Step 1: Test Your Current Setup
```bash
# Test if the plugin will work with your configuration
node test-ytdl-plugin.js
```

### Step 2: Plugin is Ready!
The plugin is already installed at `plugins/ytdl-enhanced.js` and will be automatically loaded when you restart your bot.

### Step 3: Restart Your Bot
```bash
# Stop your current bot sessions
# Then restart using your dual session setup
node start-dual-sessions.js
```

## 📝 Commands & Usage

### 🎬 Video Downloads

```
.ytdl <YouTube URL>
```
Downloads video with quality selection menu

```
.ytdl <search term>
```
Search YouTube and select video to download

```
.ytdl <search term> auto
```
Auto-download best quality of first search result

### 🎵 Audio Downloads

```
.ytdl audio <YouTube URL>
```
Download audio only from YouTube URL

```
.ytdl audio <search term>
```
Search and select audio to download

```
.ytdl audio <search term> auto
```
Auto-download audio of first search result

## 💡 Examples

```
.ytdl https://youtu.be/dQw4w9WgXcQ
.ytdl never gonna give you up auto
.ytdl jingle bells
.ytdl audio despacito
.ytdl audio christmas songs auto
```

## ⚙️ Features

### 🎯 Smart Quality Selection
- Automatically tries best available quality first
- Falls back to lower qualities if needed
- Supports: 720p → 480p → 360p → 240p → 144p

### 🔍 Interactive Search
- Search YouTube by keywords
- Shows results with duration, views, and author
- Click to select and download

### ⚡ Auto Download
- Add "auto" to any search to download first result
- Perfect for quick downloads
- Works for both video and audio

### 🛡️ Error Handling
- Multiple fallback methods
- Clear error messages
- Helpful troubleshooting tips

## 🔧 Configuration

### YouTube Cookie (Optional but Recommended)
Your YouTube cookie is already configured in your `config.json`:

```json
"YT_COOKIE": "your_cookie_here"
```

This helps with:
- Better download success rates
- Access to age-restricted content
- Faster processing

## 🧪 Testing

Run the test script to verify everything works:

```bash
node test-ytdl-plugin.js
```

Expected output:
```
✅ YouTube Cookie FOUND
✅ Search SUCCESS: Found X results
✅ Video Info SUCCESS
✅ Y2Mate Video SUCCESS
✅ Y2Mate Audio SUCCESS

🎯 Overall: 5/5 tests passed
🎉 Plugin should work well!
```

## 🚨 Troubleshooting

### If Downloads Fail:
1. **Check Internet Connection**
   ```bash
   ping google.com
   ```

2. **Test Plugin Components**
   ```bash
   node test-ytdl-plugin.js
   ```

3. **Check YouTube Cookie**
   - Make sure YT_COOKIE is set in config.json
   - Cookie should be recent (not expired)

4. **Try Different Videos**
   - Some videos may be region-blocked
   - Try popular, unrestricted videos first

### Common Issues:

**"No download URL returned"**
- Video might be private or deleted
- Try a different video
- Check your YouTube cookie

**"Search failed"**
- Check internet connection
- Try simpler search terms
- Restart the bot

**"All quality options failed"**
- Video might be too long or restricted
- Try audio download instead: `.ytdl audio <search>`

## 📊 Success Indicators

✅ **Plugin is working when:**
- Search returns results quickly
- Downloads start within 10-15 seconds
- Videos/audio files are sent successfully
- Quality selection works properly

❌ **Check configuration if:**
- Commands don't respond
- All downloads fail
- Search returns no results
- Error messages appear frequently

## 🎉 You're All Set!

Your enhanced YouTube downloader is ready to use! Try these commands:

```
.ytdl never gonna give you up auto
.ytdl audio despacito auto
```

Enjoy downloading! 🎵🎬
