const { Client, logger } = require('./lib/client')
const { DATABASE, VERSION } = require('./config')
const { stopInstance } = require('./lib/pm2')
const path = require('path')
const fs = require('fs')

// Enhanced multi-session support with isolation
const sessionId = process.env.SESSION_ID || 'default'
const authPath = process.env.AUTH_PATH || path.join(__dirname, 'auth', sessionId)

// Create session-specific directories
const sessionDirs = {
  auth: authPath,
  temp: path.join(__dirname, 'temp', sessionId),
  logs: path.join(__dirname, 'logs', sessionId),
  cache: path.join(__dirname, 'cache', sessionId)
}

// Ensure all session directories exist
Object.values(sessionDirs).forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
  }
})

// Session startup delay to prevent conflicts
const getSessionDelay = (sessionId) => {
  const sessionNumber = sessionId.replace(/\D/g, '') || '1'
  return (parseInt(sessionNumber) - 1) * 30000 // 30 seconds between sessions
}

const start = async () => {
  const delay = getSessionDelay(sessionId)

  if (delay > 0) {
    logger.info(`Session ${sessionId} waiting ${delay/1000}s before starting...`)
    await new Promise(resolve => setTimeout(resolve, delay))
  }

  logger.info(`levanter ${VERSION}`)
  logger.info(`Session: ${sessionId}`)
  logger.info(`Auth path: ${authPath}`)
  logger.info(`Session directories created: ${Object.keys(sessionDirs).join(', ')}`)

  try {
    await DATABASE.authenticate({ retry: { max: 3 } })
  } catch (error) {
    const databaseUrl = process.env.DATABASE_URL
    logger.error({ msg: 'Unable to connect to the database', error: error.message, databaseUrl })
    return stopInstance()
  }

  try {
    // Enhanced client configuration with session isolation
    const clientConfig = {
      sessionId,
      authPath,
      tempDir: sessionDirs.temp,
      logDir: sessionDirs.logs,
      cacheDir: sessionDirs.cache,
      // Add session-specific browser options
      browser: [`Levanter-${sessionId}`, 'Chrome', '1.0.0'],
      // Prevent session conflicts
      markOnlineOnConnect: false,
      syncFullHistory: false,
      // Session-specific timeouts
      connectTimeoutMs: 60000,
      defaultQueryTimeoutMs: 60000,
      // Reduce aggressive reconnection
      retryRequestDelayMs: 5000,
      maxMsgRetryCount: 3
    }

    const bot = new Client(clientConfig)
    await bot.connect()
  } catch (error) {
    logger.error(`Session ${sessionId} error:`, error)

    // Auto-restart with delay if connection fails
    setTimeout(() => {
      logger.info(`Restarting session ${sessionId}...`)
      start()
    }, 10000)
  }
}
start()
