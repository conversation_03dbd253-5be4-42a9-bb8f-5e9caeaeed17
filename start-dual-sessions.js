#!/usr/bin/env node

/**
 * 🤖 Levanter Dual Session Starter
 * 
 * This script starts both WhatsApp sessions with proper delays and isolation
 * to prevent the logout issue you've been experiencing.
 */

const { spawn } = require('child_process')
const path = require('path')
const fs = require('fs')

console.log('🤖 Levanter Dual Session Manager')
console.log('=================================')

// Load session configurations from config.json
const configPath = path.join(__dirname, 'config.json')
if (!fs.existsSync(configPath)) {
  console.error('❌ config.json not found!')
  console.log('Please make sure config.json exists with your session configurations.')
  process.exit(1)
}

const sessionConfigs = JSON.parse(fs.readFileSync(configPath, 'utf8'))

// Get valid sessions (exclude session3 as it has placeholder SESSION_ID)
const validSessions = Object.keys(sessionConfigs).filter(sessionName => {
  const config = sessionConfigs[sessionName]
  return config.SESSION_ID && 
         config.SESSION_ID !== 'levanter_sessionid' && 
         config.SESSION_ID.length > 20
})

console.log(`📋 Found ${validSessions.length} valid session(s): ${validSessions.join(', ')}`)

if (validSessions.length === 0) {
  console.error('❌ No valid sessions found!')
  console.log('Please check your config.json file and ensure SESSION_IDs are properly set.')
  process.exit(1)
}

// Create session directories
const createSessionDirectories = (sessionName) => {
  const dirs = [
    path.join(__dirname, 'auth', sessionName),
    path.join(__dirname, 'temp', sessionName),
    path.join(__dirname, 'logs', sessionName),
    path.join(__dirname, 'cache', sessionName)
  ]
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
      console.log(`📁 Created directory: ${dir}`)
    }
  })
}

// Start a single session with proper isolation
const startSession = (sessionName, sessionConfig, delay = 0) => {
  setTimeout(() => {
    console.log(`🚀 Starting ${sessionName} (delay: ${delay/1000}s)...`)
    
    // Create session directories
    createSessionDirectories(sessionName)
    
    // Enhanced environment variables with session isolation
    const env = {
      ...process.env,
      ...sessionConfig,
      // Session identification
      SESSION_NAME: sessionName,
      SESSION_ID: sessionConfig.SESSION_ID,
      // Session-specific paths
      AUTH_PATH: path.join(__dirname, 'auth', sessionName),
      TEMP_DIR: path.join(__dirname, 'temp', sessionName),
      LOG_DIR: path.join(__dirname, 'logs', sessionName),
      CACHE_DIR: path.join(__dirname, 'cache', sessionName),
      // Unique database path
      DATABASE_URL: path.join(__dirname, `database_${sessionName}.db`),
      // Browser isolation
      BROWSER_NAME: `Levanter-${sessionName}`,
      // Port isolation
      PORT: 3000 + validSessions.indexOf(sessionName),
      // Prevent aggressive reconnection
      RECONNECT_DELAY: '15000',
      MAX_RECONNECT_ATTEMPTS: '3',
      // Session startup delay
      STARTUP_DELAY: delay.toString()
    }
    
    // Start the session process
    const child = spawn('node', ['index.js'], {
      env,
      stdio: ['inherit', 'pipe', 'pipe'],
      cwd: __dirname
    })
    
    // Handle session output
    child.stdout.on('data', (data) => {
      const output = data.toString().trim()
      if (output) {
        console.log(`[${sessionName}] ${output}`)
        
        // Log to session-specific file
        const logFile = path.join(__dirname, 'logs', sessionName, 'output.log')
        fs.appendFileSync(logFile, `${new Date().toISOString()} ${output}\n`)
      }
    })
    
    child.stderr.on('data', (data) => {
      const error = data.toString().trim()
      if (error) {
        console.error(`[${sessionName}] ERROR: ${error}`)
        
        // Log errors to session-specific file
        const errorFile = path.join(__dirname, 'logs', sessionName, 'error.log')
        fs.appendFileSync(errorFile, `${new Date().toISOString()} ${error}\n`)
      }
    })
    
    child.on('error', (error) => {
      console.error(`❌ ${sessionName} failed to start:`, error.message)
    })
    
    child.on('exit', (code, signal) => {
      console.log(`⚠️ ${sessionName} exited with code ${code}, signal ${signal}`)
      
      // Auto-restart after 15 seconds if not manually stopped
      if (code !== 0 && signal !== 'SIGTERM' && signal !== 'SIGINT') {
        console.log(`🔄 Restarting ${sessionName} in 15 seconds...`)
        setTimeout(() => {
          startSession(sessionName, sessionConfig, 5000) // 5s delay on restart
        }, 15000)
      }
    })
    
    // Store process reference for cleanup
    global[`${sessionName}_process`] = child
    
    console.log(`✅ ${sessionName} started successfully!`)
    
  }, delay)
}

// Graceful shutdown handler
const gracefulShutdown = () => {
  console.log('\n🛑 Shutting down all sessions...')
  
  validSessions.forEach(sessionName => {
    const process = global[`${sessionName}_process`]
    if (process && !process.killed) {
      console.log(`🛑 Stopping ${sessionName}...`)
      process.kill('SIGTERM')
    }
  })
  
  setTimeout(() => {
    console.log('👋 All sessions stopped. Goodbye!')
    process.exit(0)
  }, 5000)
}

// Handle shutdown signals
process.on('SIGINT', gracefulShutdown)
process.on('SIGTERM', gracefulShutdown)

// Start all sessions with delays
console.log('\n🚀 Starting sessions with anti-logout delays...')
validSessions.forEach((sessionName, index) => {
  const sessionConfig = sessionConfigs[sessionName]
  const delay = index * 45000 // 45 seconds between each session (increased for better stability)
  
  console.log(`⏰ ${sessionName} will start in ${delay/1000} seconds`)
  startSession(sessionName, sessionConfig, delay)
})

console.log('\n📝 Useful Commands:')
console.log('   • Ctrl+C to stop all sessions')
console.log('   • Check logs in ./logs/[session_name]/')
console.log('   • Monitor with: node session-monitor.js')
console.log('\n✅ Dual session manager is running!')
