const fs = require('fs')
const path = require('path')

// Load session configurations
const configPath = path.join(__dirname, 'config.json')
let sessionConfigs = {}

if (fs.existsSync(configPath)) {
  try {
    sessionConfigs = JSON.parse(fs.readFileSync(configPath, 'utf8'))
  } catch (error) {
    console.error('Failed to load config.json:', error.message)
    process.exit(1)
  }
} else {
  console.error('config.json not found. Please create it with your session configurations.')
  process.exit(1)
}

// Generate PM2 apps configuration
const apps = []

Object.keys(sessionConfigs).forEach(sessionName => {
  const sessionConfig = sessionConfigs[sessionName]
  
  if (!sessionConfig.SESSION_ID || sessionConfig.SESSION_ID === 'levanter_sessionid') {
    console.warn(`Skipping ${sessionName}: Invalid or placeholder SESSION_ID`)
    return
  }

  // Create unique database path for each session
  const dbPath = path.join(__dirname, `database_${sessionName}.db`)
  
  apps.push({
    name: `levanter-${sessionName}`,
    script: 'index.js',
    cwd: __dirname,
    env: {
      ...sessionConfig,
      DATABASE_URL: dbPath,
      NODE_ENV: 'production'
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    error_file: `logs/${sessionName}-error.log`,
    out_file: `logs/${sessionName}-out.log`,
    log_file: `logs/${sessionName}-combined.log`,
    time: true
  })
})

module.exports = {
  apps
}
