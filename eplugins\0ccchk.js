const { bot } = require("../lib");
const axios = require("axios");

bot(
  {
    pattern: "ccchk ?(.*)",
    fromMe: true,
    desc: "Check if a Credit Card is Live or Dead",
    type: "tools",
  },
  async (message, match) => {
    if (!match) {
      return await message.send("❌ *Usage:* .ccchk <credit_card_details>");
    }

    try {
      const apiUrl = `https://drlabapis.onrender.com/api/chk?cc=${encodeURIComponent(match)}`;
      const response = await axios.get(apiUrl);

      if (!response.data || !response.data.status) throw "🚨 *Error checking card!*";

      let status = response.data.response.toLowerCase() === "live" ? "🟢 *LIVE*" : "🔴 *DEAD*";
      let cardDetails = response.data.card;

      await message.send(`✅ *Card Check Result:*\n\n💳 *Card:* ${cardDetails}\n📌 *Status:* ${status}`);
    } catch (error) {
      await message.send(`🚨 *Error:*\n${error}`);
    }
  }
);