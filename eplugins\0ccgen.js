const { bot } = require("../lib");
const axios = require("axios");

bot(
  {
    pattern: "ccgen ?(.*)",
    fromMe: true,
    desc: "Generate Credit Cards using BIN",
    type: "tools",
  },
  async (message, match) => {
    if (!match) {
      return await message.send("❌ *Usage:* .ccgen <bin> [count]");
    }

    let [bin, count] = match.split(" ");
    count = count || 10; // Default count = 10

    try {
      const apiUrl = `https://drlabapis.onrender.com/api/ccgenerator?bin=${bin}&count=${count}`;
      const response = await axios.get(apiUrl);

      if (!response.data) throw "🚨 *Error generating cards!*";

      const cards = response.data.split("\n").map((c, i) => `💳 *Card ${i + 1}:* \n➖ ${c}`).join("\n\n");

      await message.send(`✅ *Generated Cards:*\n\n${cards}`);
    } catch (error) {
      await message.send(`🚨 *Error:*\n${error}`);
    }
  }
);