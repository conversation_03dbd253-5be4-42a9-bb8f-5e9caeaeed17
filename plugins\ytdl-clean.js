/**
 * 🎬 Clean YouTube Downloader Plugin
 * 
 * A single, conflict-free YouTube downloader that won't cause logout issues.
 * Replaces all other YouTube plugins to prevent conflicts.
 * 
 * Commands:
 * - .ytdl <url/search> - Download video with quality options
 * - .ytdl <search> auto - Auto download best quality video
 * - .ytdl audio <url/search> - Download audio only
 * - .ytdl audio <search> auto - Auto download audio
 */

const { bot, yts, y2mate, getBuffer, isUrl, generateList, addAudioMetaData } = require('../lib/')

// YouTube URL regex pattern
const ytIdRegex = /(?:http(?:s|):\/\/|)(?:(?:www\.|)youtube(?:\-nocookie|)\.com\/(?:watch\?.*(?:|\&)v=|embed|shorts\/|v\/)|youtu\.be\/)([-_0-9A-Za-z]{11})/

// Safe text formatting to prevent errors
const safeFormat = (text) => {
  if (!text) return 'Unknown'
  return String(text).replace(/[*_`~]/g, '').substring(0, 100)
}

// Main YouTube Downloader Command
bot(
  {
    pattern: 'ytdl ?(.*)',
    desc: 'Download YouTube videos and audio',
    type: 'download',
  },
  async (message, match) => {
    try {
      match = match || message.reply_message?.text
      if (!match) {
        return await message.send(`*🎬 YouTube Downloader*

*📥 Video Downloads:*
• \`.ytdl <YouTube URL>\` - Download with quality options
• \`.ytdl <search term>\` - Search and select video
• \`.ytdl <search term> auto\` - Auto download best quality

*🎵 Audio Downloads:*
• \`.ytdl audio <YouTube URL>\` - Download audio only
• \`.ytdl audio <search term>\` - Search and select audio
• \`.ytdl audio <search term> auto\` - Auto download audio

*📝 Examples:*
\`.ytdl https://youtu.be/dQw4w9WgXcQ\`
\`.ytdl never gonna give you up auto\`
\`.ytdl audio despacito\``)
      }

      // Check if it's an audio download request
      const isAudioRequest = match.toLowerCase().startsWith('audio ')
      if (isAudioRequest) {
        match = match.substring(6).trim() // Remove "audio " prefix
        return await handleAudioDownload(message, match)
      }

      // Handle video download
      return await handleVideoDownload(message, match)
      
    } catch (error) {
      console.error('YTDL Plugin Error:', error)
      return await message.send(`❌ *Plugin Error:* ${error.message}`)
    }
  }
)

// Handle video downloads
async function handleVideoDownload(message, match) {
  try {
    const vid = ytIdRegex.exec(match)
    const isAutoDownload = match.toLowerCase().includes(' auto')
    const searchTerm = match.replace(/ auto$/i, '').trim()

    // If not a direct URL, search first
    if (!vid) {
      await message.send('🔍 *Searching YouTube...*')
      const result = await yts(searchTerm, false, null, message.id)
      
      if (!result || !result.length) {
        return await message.send(`❌ *No results found for:* ${searchTerm}`)
      }

      if (isAutoDownload) {
        // Auto download first result
        const topResult = result[0]
        await message.send(`🎬 *Auto-downloading:* ${safeFormat(topResult.title)}`)
        return await downloadVideo(message, topResult.id, topResult)
      } else {
        // Show search results for selection
        const msg = generateList(
          result.slice(0, 8).map(({ title, id, duration, view, author }) => ({
            text: `🎬 ${safeFormat(title)}\n⏱️ ${duration || 'Unknown'} | 👁️ ${view || 'Unknown'}\n👤 ${safeFormat(author)}\n`,
            id: `ytdl https://www.youtube.com/watch?v=${id}`,
          })),
          `🔍 *Search Results for:* ${searchTerm}\n\nSelect video to download:`,
          message.jid,
          message.participant,
          message.id
        )
        return await message.send(msg.message, { quoted: message.data }, msg.type)
      }
    }

    // Direct URL download
    const videoId = vid[1]
    
    await message.send('📋 *Getting video info...*')
    const [videoInfo] = await yts(videoId, true, null, message.id)
    return await downloadVideo(message, videoId, videoInfo)
    
  } catch (error) {
    console.error('Video Download Error:', error)
    return await message.send(`❌ *Video download failed:* ${error.message}`)
  }
}

// Handle audio downloads
async function handleAudioDownload(message, match) {
  try {
    const vid = ytIdRegex.exec(match)
    const isAutoDownload = match.toLowerCase().includes(' auto')
    const searchTerm = match.replace(/ auto$/i, '').trim()

    // If not a direct URL, search first
    if (!vid) {
      await message.send('🔍 *Searching YouTube for audio...*')
      const result = await yts(searchTerm, false, null, message.id)
      
      if (!result || !result.length) {
        return await message.send(`❌ *No results found for:* ${searchTerm}`)
      }

      if (isAutoDownload) {
        // Auto download first result
        const topResult = result[0]
        await message.send(`🎵 *Auto-downloading audio:* ${safeFormat(topResult.title)}`)
        return await downloadAudio(message, topResult.id, topResult)
      } else {
        // Show search results for selection
        const msg = generateList(
          result.slice(0, 8).map(({ title, id, duration, view, author }) => ({
            text: `🎵 ${safeFormat(title)}\n⏱️ ${duration || 'Unknown'} | 👁️ ${view || 'Unknown'}\n👤 ${safeFormat(author)}\n`,
            id: `ytdl audio https://www.youtube.com/watch?v=${id}`,
          })),
          `🔍 *Audio Search Results for:* ${searchTerm}\n\nSelect audio to download:`,
          message.jid,
          message.participant,
          message.id
        )
        return await message.send(msg.message, { quoted: message.data }, msg.type)
      }
    }

    // Direct URL audio download
    const videoId = vid[1]
    
    await message.send('📋 *Getting audio info...*')
    const [videoInfo] = await yts(videoId, true, null, message.id)
    return await downloadAudio(message, videoId, videoInfo)
    
  } catch (error) {
    console.error('Audio Download Error:', error)
    return await message.send(`❌ *Audio download failed:* ${error.message}`)
  }
}

// Download video with quality options
async function downloadVideo(message, videoId, videoInfo) {
  try {
    const safeTitle = safeFormat(videoInfo.title)
    const safeAuthor = safeFormat(videoInfo.author)
    const safeDuration = videoInfo.duration || 'Unknown'

    await message.send(`🎬 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n\n🔄 *Starting download...*`)

    // Try y2mate.get first (simpler method)
    const result = await y2mate.get(videoId, 'video')
    
    if (isUrl(result)) {
      await message.send('✅ *Success! Sending video...*')
      return await message.sendFromUrl(result, {
        quoted: message.data,
        caption: `🎬 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}`
      })
    }
    
    // If that fails, try with quality selection
    const qualities = ['720', '480', '360', '240']
    
    for (const quality of qualities) {
      try {
        const qualityResult = await y2mate.dl(videoId, 'video', quality)
        if (qualityResult && isUrl(qualityResult)) {
          await message.send(`✅ *Success! Sending ${quality}p video...*`)
          return await message.sendFromUrl(qualityResult, {
            quoted: message.data,
            caption: `🎬 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n📺 Quality: ${quality}p`
          })
        }
      } catch (qualityError) {
        continue // Try next quality
      }
    }
    
    throw new Error('All download methods failed')
    
  } catch (error) {
    console.error('Video Download Function Error:', error)
    return await message.send(`❌ *Video download failed*\n\n💡 *Try:*\n• Different video\n• Audio download: \`.ytdl audio ${videoId}\``)
  }
}

// Download audio
async function downloadAudio(message, videoId, videoInfo) {
  try {
    const safeTitle = safeFormat(videoInfo.title)
    const safeAuthor = safeFormat(videoInfo.author)
    const safeDuration = videoInfo.duration || 'Unknown'

    await message.send(`🎵 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n\n🔄 *Downloading audio...*`)

    // Try y2mate.get for audio
    const result = await y2mate.get(videoId, 'audio')
    
    if (isUrl(result)) {
      await message.send('✅ *Success! Sending audio...*')
      return await message.sendFromUrl(result, {
        quoted: message.data,
        mimetype: 'audio/mpeg',
        fileName: `${safeTitle}.mp3`
      })
    }
    
    // Try y2mate.dl as fallback
    const downloadResult = await y2mate.dl(videoId, 'audio')
    if (downloadResult && isUrl(downloadResult)) {
      await message.send('✅ *Success! Sending audio...*')
      
      // Try to add metadata if possible
      try {
        const { buffer } = await getBuffer(downloadResult)
        if (buffer) {
          const audioWithMeta = await addAudioMetaData(
            buffer, 
            safeTitle, 
            safeAuthor, 
            '', 
            videoInfo.thumbnail?.url || ''
          )
          return await message.send(audioWithMeta, {
            quoted: message.data,
            mimetype: 'audio/mpeg'
          }, 'audio')
        }
      } catch (metaError) {
        // If metadata fails, send without it
        console.log('Metadata failed, sending without:', metaError.message)
      }
      
      return await message.sendFromUrl(downloadResult, {
        quoted: message.data,
        mimetype: 'audio/mpeg',
        fileName: `${safeTitle}.mp3`
      })
    }
    
    throw new Error('No download URL returned')
    
  } catch (error) {
    console.error('Audio Download Function Error:', error)
    return await message.send(`❌ *Audio download failed*\n\n💡 *Try:*\n• Different video\n• Video download: \`.ytdl ${videoId}\``)
  }
}
