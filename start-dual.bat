@echo off
echo 🤖 Levanter Dual Session Quick Start
echo ===================================

REM Check if Node.js is installed
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if config.json exists
if not exist "config.json" (
    echo ❌ config.json not found
    echo Please make sure config.json exists with your session configurations
    pause
    exit /b 1
)

echo 🔍 Running session health check...
node check-sessions.js

echo.
echo 🚀 Starting dual sessions...
echo ⚠️  IMPORTANT: Do NOT close this window while sessions are running
echo.

REM Start the dual session manager
node start-dual-sessions.js

pause
