#!/usr/bin/env node

/**
 * Multi-Session Startup Manager
 * 
 * This script starts multiple WhatsApp sessions with proper delays
 * to prevent logout issues.
 */

const { spawn } = require('child_process')
const path = require('path')
const fs = require('fs')

// Configuration
const SESSIONS = [
  { id: 'session1', delay: 0 },      // Start immediately
  { id: 'session2', delay: 30000 }   // Start after 30 seconds
]

// Ensure auth directories exist
SESSIONS.forEach(session => {
  const authDir = path.join(__dirname, 'auth', session.id)
  if (!fs.existsSync(authDir)) {
    fs.mkdirSync(authDir, { recursive: true })
    console.log(`✅ Created auth directory: ${authDir}`)
  }
})

function startSession(sessionConfig) {
  const { id, delay } = sessionConfig
  
  setTimeout(() => {
    console.log(`🚀 Starting ${id}...`)
    
    const env = {
      ...process.env,
      SESSION_ID: id,
      AUTH_PATH: path.join(__dirname, 'auth', id)
    }
    
    const child = spawn('node', ['index.js'], {
      env,
      stdio: 'inherit',
      cwd: __dirname
    })
    
    child.on('error', (error) => {
      console.error(`❌ ${id} error:`, error.message)
    })
    
    child.on('exit', (code, signal) => {
      console.log(`⚠️ ${id} exited with code ${code}, signal ${signal}`)
      
      // Auto-restart after 10 seconds if not manually stopped
      if (code !== 0 && signal !== 'SIGTERM' && signal !== 'SIGINT') {
        console.log(`🔄 Restarting ${id} in 10 seconds...`)
        setTimeout(() => startSession(sessionConfig), 10000)
      }
    })
    
    // Store process reference for cleanup
    process[`${id}_process`] = child
    
  }, delay)
}

// Graceful shutdown handler
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down all sessions...')
  
  SESSIONS.forEach(session => {
    const childProcess = process[`${session.id}_process`]
    if (childProcess && !childProcess.killed) {
      console.log(`🔴 Stopping ${session.id}...`)
      childProcess.kill('SIGTERM')
    }
  })
  
  setTimeout(() => {
    console.log('✅ All sessions stopped')
    process.exit(0)
  }, 5000)
})

// Start all sessions
console.log('🎯 Multi-Session WhatsApp Bot Manager')
console.log('=====================================')
console.log(`📱 Starting ${SESSIONS.length} sessions...`)
console.log('⏹️  Press Ctrl+C to stop all sessions\n')

SESSIONS.forEach(startSession)

// Keep the main process alive
process.stdin.resume()
