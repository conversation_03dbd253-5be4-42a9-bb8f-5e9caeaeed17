#!/usr/bin/env node

/**
 * 🔍 Quick Plugin Verification
 */

console.log('🔍 Verifying Enhanced YTDL Plugin Setup')
console.log('======================================\n')

const fs = require('fs')
const path = require('path')

// Check plugin file
const pluginPath = path.join(__dirname, 'plugins', 'ytdl-enhanced.js')
if (fs.existsSync(pluginPath)) {
  console.log('✅ Plugin file: Found at plugins/ytdl-enhanced.js')
} else {
  console.log('❌ Plugin file: Missing')
  process.exit(1)
}

// Check config.json
const configJsonPath = path.join(__dirname, 'config.json')
if (fs.existsSync(configJsonPath)) {
  try {
    const configJson = JSON.parse(fs.readFileSync(configJsonPath, 'utf8'))
    const validSessions = Object.keys(configJson).filter(sessionName => {
      const config = configJson[sessionName]
      return config.SESSION_ID && 
             config.SESSION_ID !== 'levanter_sessionid' && 
             config.SESSION_ID.length > 20
    })
    console.log(`✅ config.json: Found with ${validSessions.length} valid sessions`)
  } catch (error) {
    console.log('❌ config.json: Invalid JSON format')
  }
} else {
  console.log('❌ config.json: Missing')
}

// Check config.env
const configEnvPath = path.join(__dirname, 'config.env')
if (fs.existsSync(configEnvPath)) {
  const configEnv = fs.readFileSync(configEnvPath, 'utf8')
  if (configEnv.includes('YT_COOKIE') && configEnv.includes('SESSION_ID')) {
    console.log('✅ config.env: Found with YouTube cookie')
  } else {
    console.log('⚠️ config.env: Found but missing YouTube cookie')
  }
} else {
  console.log('❌ config.env: Missing')
}

// Check dual session scripts
const dualSessionScript = path.join(__dirname, 'start-dual-sessions.js')
if (fs.existsSync(dualSessionScript)) {
  console.log('✅ Dual session script: Found')
} else {
  console.log('⚠️ Dual session script: Missing')
}

console.log('\n🎉 Plugin Setup Complete!')
console.log('\n📝 Next Steps:')
console.log('   1. Start your bot: node start-dual-sessions.js')
console.log('   2. Wait for both sessions to connect')
console.log('   3. Try the plugin: .ytdl never gonna give you up auto')
console.log('\n💡 Plugin Commands:')
console.log('   .ytdl <YouTube URL>')
console.log('   .ytdl <search term>')
console.log('   .ytdl <search term> auto')
console.log('   .ytdl audio <YouTube URL>')
console.log('   .ytdl audio <search term>')
console.log('   .ytdl audio <search term> auto')
console.log('\n🎵 Example Usage:')
console.log('   .ytdl https://youtu.be/dQw4w9WgXcQ')
console.log('   .ytdl never gonna give you up auto')
console.log('   .ytdl audio despacito')
console.log('   .ytdl audio christmas songs auto')

console.log('\n✅ Your Enhanced YouTube Downloader is ready to use!')
