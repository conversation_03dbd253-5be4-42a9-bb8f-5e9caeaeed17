{"name": "levanter", "version": "3.8.6", "description": "", "main": "index.js", "scripts": {"start": "pm2 start . --attach --name levanter", "docker": "pm2-runtime start . --name levanter", "stop": "pm2 stop levanter", "multi": "node multi-session.js", "multi-pm2": "pm2 start ecosystem.config.js", "multi-stop": "pm2 delete ecosystem.config.js", "multi-status": "pm2 status", "multi-logs": "pm2 logs", "postinstall": "node -e \"try { require('sharp') } catch (e) { process.exit(1); }\" || yarn add --ignore-engines sharp@0.33.5"}, "author": "", "license": "MIT", "dependencies": {"audio-decode": "^2.2.2", "axios": "^1.3.3", "baileys": "git+https://<EMAIL>/lyfe00011/baileys.git", "browser-id3-writer": "^4.4.0", "cheerio": "^1.0.0-rc.10", "cookie": "^0.5.0", "cron": "^3.1.7", "dotenv": "^16.4.5", "file-type": "^18.2.1", "fluent-ffmpeg": "^2.1.2", "form-data": "^4.0.0", "fs-extra": "^11.1.0", "google-tts-api": "^2.0.2", "googleapis": "^126.0.1", "heroku-client": "^3.1.0", "jimp": "^0.16.1", "link-preview-js": "^3.0.4", "moment": "^2.29.4", "node-fetch": "3.2.2", "node-webpmux": "^3.1.4", "pdfkit": "^0.13.0", "pg": "^8.7.3", "pino-pretty": "^9.1.1", "pm2": "^6.0.5", "qrcode-reader": "^1.0.4", "qrcode-terminal": "^0.12.0", "sequelize": "^6.21.4", "sharp": "^0.33.5", "simple-git": "^3.16.1", "sqlite3": "^5.0.11", "translate-google-api": "^1.0.4", "youtubei.js": "^13.4.0"}, "packageManager": "yarn@1.22.22", "engines": {"node": ">=20.0.0"}}