# YouTube Download Setup Guide

## Issues Fixed

### 1. ✅ Game Plugin Conflict Resolved
- **Problem**: Duplicate game files (`plugins/games.js` and `games-plugin.js`) were conflicting
- **Solution**: Removed the duplicate `games-plugin.js` file
- **Result**: Game commands like `.wordchain`, `.trivia`, `.tod` should now work properly

### 2. 🔧 YouTube Download Enhanced
- **Problem**: YouTube download methods failing
- **Solution**: Added multiple fallback methods with better error handling
- **Methods Added**:
  1. Y2mate API (primary)
  2. Cobalt Tools API
  3. YT-DLP API
  4. SaveFrom API
  5. YouTube.js API
  6. Alternative YT API
  7. Cookie-enhanced method (if cookie provided)

## Testing YouTube Downloads

### Quick Test
Run this command to test which download methods are working:
```bash
node test-ytdl.js
```

This will test all the download APIs and show you which ones are currently working.

### Manual Testing
Try these commands in your bot:
```
.ytdl https://youtu.be/dQw4w9WgXcQ
.ytmp3 https://youtu.be/dQw4w9WgXcQ
.ytdl jingle bells auto
```

## Adding YouTube Cookie (Optional)

If you have a YouTube cookie for better access:

1. **Set Environment Variable**:
   ```bash
   # Windows
   set YOUTUBE_COOKIE="your_cookie_here"
   
   # Linux/Mac
   export YOUTUBE_COOKIE="your_cookie_here"
   ```

2. **Or add to your .env file**:
   ```
   YOUTUBE_COOKIE=your_cookie_here
   ```

### How to Get YouTube Cookie:
1. Open YouTube in your browser
2. Open Developer Tools (F12)
3. Go to Network tab
4. Refresh the page
5. Find a request to YouTube
6. Copy the Cookie header value

## Game Commands Available

### Word Chain Game
- `.wordchain start` - Start a new game
- `.wordchain join` - Join existing game
- `.wordchain end` - End current game
- `.wordchain score` - View leaderboard

### Trivia Game
- `.trivia start` - Start trivia question
- `.trivia score` - View trivia scores
- Answer with numbers 1-4

### Truth or Dare
- `.tod` - Random truth or dare
- `.tod truth` - Get a truth question
- `.tod dare` - Get a dare challenge

### Other Games
- `.wyr` - Would You Rather questions
- `.emojipuzzle` - Emoji guessing game
- `.emojipuzzle movies` - Movie emoji puzzles
- `.tictactoe @user` - Play tic-tac-toe

## Troubleshooting

### If YouTube downloads still fail:
1. Run the test script: `node test-ytdl.js`
2. Check if any methods are working
3. Try adding a YouTube cookie
4. Wait a few minutes and try again (APIs might be temporarily down)

### If games don't work:
1. Make sure there's only one games.js file in the plugins directory
2. Restart your bot
3. Check for any error messages in the console

### Common Issues:
- **"All methods failed"**: YouTube APIs are temporarily down, try later
- **"No results found"**: Search term might be too specific
- **Game commands not responding**: Check for plugin conflicts

## Support

If you're still having issues:
1. Share the output of `node test-ytdl.js`
2. Share any error messages from the bot console
3. Let me know which specific commands aren't working

The enhanced ytdl plugin now has 7 different fallback methods, so it should be much more reliable!
