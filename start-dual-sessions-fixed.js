#!/usr/bin/env node

/**
 * 🤖 Anti-Logout Dual Session Starter
 * 
 * This script prevents the "Intentional Logout" error by:
 * - Starting sessions with 2-minute delays
 * - Using different browser profiles
 * - Implementing proper session isolation
 * - Adding connection retry logic
 */

const { spawn } = require('child_process')
const path = require('path')
const fs = require('fs')

console.log('🤖 Anti-Logout Dual Session Manager')
console.log('===================================')

// Load session configurations
const configPath = path.join(__dirname, 'config.json')
if (!fs.existsSync(configPath)) {
  console.error('❌ config.json not found!')
  process.exit(1)
}

const sessionConfigs = JSON.parse(fs.readFileSync(configPath, 'utf8'))

// Get valid sessions
const validSessions = Object.keys(sessionConfigs).filter(sessionName => {
  const config = sessionConfigs[sessionName]
  return config.SESSION_ID && 
         config.SESSION_ID !== 'levanter_sessionid' && 
         config.SESSION_ID.length > 20
})

console.log(`📋 Found ${validSessions.length} valid session(s): ${validSessions.join(', ')}`)

if (validSessions.length === 0) {
  console.error('❌ No valid sessions found!')
  process.exit(1)
}

// Create session directories
const createSessionDirectories = (sessionName) => {
  const dirs = [
    path.join(__dirname, 'auth', sessionName),
    path.join(__dirname, 'temp', sessionName),
    path.join(__dirname, 'logs', sessionName)
  ]
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }
  })
}

// Start a single session with anti-logout measures
const startSession = (sessionName, sessionConfig, delay = 0) => {
  setTimeout(() => {
    console.log(`🚀 Starting ${sessionName} (delay: ${delay/1000}s)...`)
    
    createSessionDirectories(sessionName)
    
    // Anti-logout environment variables
    const env = {
      ...process.env,
      ...sessionConfig,
      // Session identification
      SESSION_NAME: sessionName,
      SESSION_ID: sessionConfig.SESSION_ID,
      // Unique paths for isolation
      AUTH_PATH: path.join(__dirname, 'auth', sessionName),
      TEMP_DIR: path.join(__dirname, 'temp', sessionName),
      LOG_DIR: path.join(__dirname, 'logs', sessionName),
      // Unique database
      DATABASE_URL: path.join(__dirname, `database_${sessionName}.db`),
      // Browser isolation (CRITICAL for preventing logout)
      BROWSER_NAME: `Levanter-${sessionName}-${Date.now()}`,
      USER_AGENT: sessionName === 'session1' ? 
        'WhatsApp/2.2316.4 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)' :
        'WhatsApp/2.2316.4 Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
      // Connection settings
      RECONNECT_DELAY: '30000', // 30 seconds
      MAX_RECONNECT_ATTEMPTS: '2',
      CONNECTION_TIMEOUT: '60000', // 1 minute
      // Anti-logout settings
      ALWAYS_ONLINE: 'false',
      AUTO_STATUS_VIEW: 'false',
      SEND_READ: 'false',
      AREACT: 'off',
      // Unique port
      PORT: 3000 + validSessions.indexOf(sessionName)
    }
    
    // Start the session process
    const child = spawn('node', ['index.js'], {
      env,
      stdio: ['inherit', 'pipe', 'pipe'],
      cwd: __dirname
    })
    
    // Handle session output
    child.stdout.on('data', (data) => {
      const output = data.toString().trim()
      if (output) {
        console.log(`[${sessionName}] ${output}`)
        
        // Check for logout warnings
        if (output.toLowerCase().includes('logout') || 
            output.toLowerCase().includes('unauthorized')) {
          console.log(`⚠️ [${sessionName}] Logout detected - will restart in 5 minutes`)
        }
      }
    })
    
    child.stderr.on('data', (data) => {
      const error = data.toString().trim()
      if (error) {
        console.error(`[${sessionName}] ERROR: ${error}`)
      }
    })
    
    child.on('exit', (code, signal) => {
      console.log(`⚠️ [${sessionName}] exited with code ${code}, signal ${signal}`)
      
      // Auto-restart with longer delay if logout occurred
      if (code !== 0 && signal !== 'SIGTERM' && signal !== 'SIGINT') {
        console.log(`🔄 Restarting ${sessionName} in 5 minutes to prevent logout...`)
        setTimeout(() => {
          startSession(sessionName, sessionConfig, 10000) // 10s delay on restart
        }, 300000) // 5 minutes
      }
    })
    
    // Store process reference
    global[`${sessionName}_process`] = child
    console.log(`✅ ${sessionName} started successfully!`)
    
  }, delay)
}

// Graceful shutdown
const gracefulShutdown = () => {
  console.log('\n🛑 Shutting down all sessions...')
  
  validSessions.forEach(sessionName => {
    const process = global[`${sessionName}_process`]
    if (process && !process.killed) {
      console.log(`🛑 Stopping ${sessionName}...`)
      process.kill('SIGTERM')
    }
  })
  
  setTimeout(() => {
    console.log('👋 All sessions stopped. Goodbye!')
    process.exit(0)
  }, 10000)
}

process.on('SIGINT', gracefulShutdown)
process.on('SIGTERM', gracefulShutdown)

// Start sessions with LONG delays to prevent logout
console.log('\n🚀 Starting sessions with anti-logout delays...')
validSessions.forEach((sessionName, index) => {
  const sessionConfig = sessionConfigs[sessionName]
  const delay = index * 120000 // 2 MINUTES between each session (critical!)
  
  console.log(`⏰ ${sessionName} will start in ${delay/1000} seconds`)
  startSession(sessionName, sessionConfig, delay)
})

console.log('\n📝 Anti-Logout Tips:')
console.log('   • Sessions start 2 minutes apart')
console.log('   • Each session has unique browser profile')
console.log('   • Auto-restart disabled for 5 minutes after logout')
console.log('   • Keep sessions running continuously')
console.log('\n✅ Anti-logout dual session manager is running!')
