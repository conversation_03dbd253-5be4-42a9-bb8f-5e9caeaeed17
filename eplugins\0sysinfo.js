// V5 uso levanter
const { bot } = require('../lib');
const os = require('os');
const { exec } = require('child_process');
const fs = require('fs').promises;
const util = require('util');
const path = require('path');
const execAsync = util.promisify(exec);

class FastFetchDownloader {
  constructor() {
    this.config = {
      binPath: path.join(process.cwd(), 'media', 'bin'),
    };

    this.fastfetchBinaries = new Map([
      ['linux-x64', {
        url: 'https://github.com/fastfetch-cli/fastfetch/releases/download/2.35.0/fastfetch-linux-amd64.tar.gz',
        relativePath: 'fastfetch-linux-amd64/usr/bin/fastfetch',
      }],
      ['linux-arm64', {
        url: 'https://github.com/fastfetch-cli/fastfetch/releases/download/2.35.0/fastfetch-linux-aarch64.tar.gz',
        relativePath: 'fastfetch-linux-aarch64/usr/bin/fastfetch',
      }],
      ['win32-x64', {
        url: 'https://github.com/fastfetch-cli/fastfetch/releases/download/2.35.0/fastfetch-windows-amd64.zip',
        relativePath: 'fastfetch-windows-amd64/fastfetch.exe',
      }],
    ]);
  }

  getPlatformInfo() {
    let platform = os.platform();
    let arch = os.arch();

    if (platform === 'android') {
      platform = 'android';
      arch = arch === 'arm64' ? 'arm64' : 'x64';
    } else if (platform === 'linux') {
      arch = (arch === 'arm64' || arch === 'aarch64') ? 'arm64' : 'x64';
    } else if (platform === 'win32') {
      arch = 'x64';
    }

    return { platform, arch };
  }

  async tryInstallFromPackageManager() {
    const { platform } = this.getPlatformInfo();
    
    try {
      if (platform === 'android') {
        await execAsync('pkg update -y && pkg install fastfetch -y');
        return true;
      } else if (platform === 'linux') {
        await execAsync('sudo apt update && sudo apt install fastfetch -y');
        return true;
      }
    } catch (error) {
      console.log('Package manager installation failed:', error.message);
      return false;
    }
    return false;
  }

  async downloadAndExtractFastFetch() {
    const { platform, arch } = this.getPlatformInfo();
    const key = `${platform === 'android' ? 'linux' : platform}-${arch}`;
    const binary = this.fastfetchBinaries.get(key);

    if (!binary) {
      throw new Error(`Unsupported System: ${key}`);
    }

    await fs.mkdir(this.config.binPath, { recursive: true });
    const downloadPath = path.join(this.config.binPath, path.basename(binary.url));
    const extractPath = this.config.binPath;

    try {
      await execAsync(`curl -fsSL -o "${downloadPath}" "${binary.url}"`);
      
      if (platform === 'win32') {
        await execAsync(`powershell -Command "Expand-Archive -Path '${downloadPath}' -DestinationPath '${extractPath}' -Force"`);
      } else {
        await execAsync(`tar xf "${downloadPath}" -C "${extractPath}"`);
      }

      const binaryPath = path.join(this.config.binPath, binary.relativePath);
      if (platform !== 'win32') {
        await fs.chmod(binaryPath, '755');
      }

      await fs.unlink(downloadPath);
      return binaryPath;
    } catch (error) {
      console.error('Error during download/extract:', error);
      throw error;
    }
  }

  async getFastFetchPath() {
    try {
      const { stdout } = await execAsync('which fastfetch');
      if (stdout.trim()) return 'fastfetch';
    } catch {}

    if (await this.tryInstallFromPackageManager()) {
      return 'fastfetch';
    }

    const { platform, arch } = this.getPlatformInfo();
    const key = `${platform === 'android' ? 'linux' : platform}-${arch}`;
    const binary = this.fastfetchBinaries.get(key);
    const localBinaryPath = path.join(this.config.binPath, binary.relativePath);

    try {
      await fs.access(localBinaryPath);
      return localBinaryPath;
    } catch {
      return await this.downloadAndExtractFastFetch();
    }
  }
}

async function safeExec(command, fallbackCommand = null) {
  try {
    const { stdout } = await execAsync(command);
    return stdout.trim();
  } catch (error) {
    if (fallbackCommand) {
      try {
        const { stdout } = await execAsync(fallbackCommand);
        return stdout.trim();
      } catch (err) {
        return null;
      }
    }
    return null;
  }
}

async function runSpeedtest(message) {
  try {
    const tempDir = process.env.TEMP_DOWNLOAD_DIR || path.join(process.cwd(), 'media');
    const speedtestPath = path.join(tempDir, 'bin', 'ookla-speedtest.py');
    
    await fs.mkdir(path.dirname(speedtestPath), { recursive: true });
    
    try {
      await fs.access(speedtestPath);
    } catch {
      const speedtestUrl = 'https://raw.githubusercontent.com/weskerty/MysticTools/refs/heads/main/Utilidades/ookla-speedtest.py';
      await execAsync(`curl -fsSL -o "${speedtestPath}" "${speedtestUrl}"`);
      await execAsync(`chmod +x ${speedtestPath}`);
    }

    const stdout = await safeExec(`python3 ${speedtestPath} --secure --share`, `python ${speedtestPath} --secure --share`);
    if (!stdout) throw new Error('Failed to run speedtest');

    const imageUrlMatch = stdout.match(/http[^"]+\.png/);
    if (imageUrlMatch) {
      const imageUrl = imageUrlMatch[0];
      const fetch = (await import('node-fetch')).default;
      const response = await fetch(imageUrl);
      const imageBuffer = Buffer.from(await response.arrayBuffer());

      await message.send(
        imageBuffer,
        {
          fileName: 'speedtest_result.png',
          mimetype: 'image/png',
          caption: stdout
        },
        'image'
      );
    } else {
      await message.send(stdout);
    }
    
    return stdout;
  } catch (error) {
    console.error('Speedtest error:', error);
    return '❌ Error in speedtest';
  }
}

async function getSoftwareVersions() {
  const versions = [];
  
  // Sudo 
  const sudoCheck = await safeExec('which sudo');
  versions.push(`*Sudo* ${sudoCheck ? '✅' : '✖'}`);
  
  // Software versions with emoji mappings
  const checks = [
    { name: 'Node.js', command: 'node -v', emoji: '🟢' },
    { name: 'NPM', command: 'npm -v', emoji: '📦' },
    { name: 'Python', command: 'python3 --version', fallback: 'python --version', emoji: '🐍' },
    { name: 'Chocolatey', command: 'choco --version', emoji: '🍫' },
    { name: 'FFmpeg', command: 'ffmpeg -version', emoji: '🎬', process: (out) => out.split('\n')[0] }
  ];
  
  // PIP check with special processing
  const pipOutput = await safeExec('pip3 --version', 'pip --version');
  let pipVersion = '✖';
  if (pipOutput) {
    const pipMatch = pipOutput.match(/pip\s+(\d+\.\d+\.\d+)/);
    pipVersion = pipMatch ? pipMatch[1] : pipOutput;
  }
  versions.push(`📊 *PIP:* ${pipVersion}`);

  // Process all other checks
  for (const check of checks) {
    const output = await safeExec(check.command, check.fallback);
    let value = output ? (check.process ? check.process(output) : output) : '✖';
    versions.push(`${check.emoji} *${check.name}:* ${value}`);
  }

  return versions.join('\n');
}

bot(
  {
    pattern: 'sysinfo ?(.*)',
    fromMe: true,
    desc: 'All System Server Info',
    type: 'machine',
  },
  async (message) => {
    try {
      // Get and run fastfetch
      const fastFetchPath = await new FastFetchDownloader().getFastFetchPath();
      const sysInfo = await safeExec(`"${fastFetchPath}" -l none -c all`);
      if (sysInfo) await message.send(sysInfo);
      else throw new Error('Failed to get system information');

      // Send software versions
      const softwareVersions = await getSoftwareVersions();
      await message.send(softwareVersions);

      // Run speedtest
      await runSpeedtest(message);
    } catch (error) {
      console.error('SYSINFO error:', error);
      await message.send(`❌ ${error.message}`);
    }
  }
);

module.exports = { FastFetchDownloader };