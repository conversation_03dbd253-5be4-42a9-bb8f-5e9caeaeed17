// Adaptado de: https://github.com/BrunoSobrino/TheMystic-Bot-MD/blob/master/plugins/buscador-tiktoksearch.js Aunque sin la Funcion "Carrusel"

const { bot, logger } = require('../lib');
const axios = require('axios');

class TikTokSearchHandler {
  constructor() {
    this.apiURL = 'https://tikwm.com/api/feed/search';
    this.headers = {
      "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
      Cookie: "current_language=en",
      "User-Agent": "Mozilla/5.0 (Linux Android 10 K) AppleWebKit/537.36 (KHTML, como Gecko) Chrome/116.0.0.0 Mobile Safari/537.36",
    };
  }

  async tiktokSearch(query) {
    try {
      const response = await axios.post(
        this.apiURL,
        new URLSearchParams({ keywords: query, count: '10', cursor: '0', HD: '1' }),
        { headers: this.headers }
      );
      const videos = response.data.data.videos;
      if (!videos || videos.length === 0) {
        return { status: false, resultado: 'No se encontraron videos.' };
      }
      return {
        status: true,
        resultado: videos.map(v => ({
          description: v.title || 'Sin descripción',
          videoUrl: v.play || 'Sin URL',
        })),
      };
    } catch (error) {
      logger.error('Error al buscar videos en TikTok:', error.message);
      return { status: false, resultado: error.message };
    }
  }

  async createVideoMessage(url, message) {
    try {
      const response = await axios.get(url, { responseType: 'arraybuffer' });
      const buffer = response.data;

      // Enviar el video directamente, No Carrusel 
      await message.send(buffer, { mimetype: 'video/mp4'}, 'video');
    } catch (error) {
      logger.error('Error al crear el mensaje de video:', error.message);
      throw new Error(`Error al crear el mensaje de video: ${error.message}`);
    }
  }

  shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
  }
}

const tiktokHandler = new TikTokSearchHandler();

bot(
  {
    pattern: 'stt ?(.*)',
    fromMe: true,
    desc: 'Search video TikTok',
    type: 'search',
  },
  async (message, match) => {
    const text = match.trim();
    if (!text) {
      await message.send('❗ What search on TikTok?');
      return;
    }

    try {
      const response = await tiktokHandler.tiktokSearch(text);
      if (!response.status) {
        throw new Error(response.resultado);
      }

      const searchResults = response.resultado;
      tiktokHandler.shuffleArray(searchResults);
      const selectedResults = searchResults.slice(0, 5); // Mostrar hasta 5 resultados

      for (const result of selectedResults) {
        await tiktokHandler.createVideoMessage(result.videoUrl, message);
      }
    } catch (error) {
      await message.send(`❌ Error: ${error.message}`);
    }
  }
);
