const { bot, yts, y2mate, getBuffer, isUrl, addAudioMetaData } = require('../lib/')

// Safe text formatting
const safeFormat = (text) => {
  if (!text) return 'Unknown'
  return String(text).replace(/[*_`~]/g, '').substring(0, 100)
}

bot(
  {
    pattern: 'play ?(.*)',
    fromMe: true,
    desc: 'Download youtube audio',
    type: 'download',
  },
  async (message, match) => {
    try {
      match = match || message.reply_message?.text
      if (!match) return await message.send('_Example : play ghost_')

      await message.send('🔍 *Searching...*')
      const result = await yts(match, false, null, message.id)

      if (!result || !result.length) {
        return await message.send(`*${match} not found*`)
      }

      const { title, id, author, duration } = result[0]
      const safeTitle = safeFormat(title)
      const safeAuthor = safeFormat(author)

      await message.send(`🎵 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${duration || 'Unknown'}\n\n🔄 *Downloading...*`)

      // Try y2mate.get for audio
      const audioResult = await y2mate.get(id, 'audio')

      if (isUrl(audioResult)) {
        await message.send('✅ *Success! Sending audio...*')
        return await message.sendFromUrl(audioResult, {
          quoted: message.data,
          mimetype: 'audio/mpeg',
          fileName: `${safeTitle}.mp3`
        })
      }

      // Try y2mate.dl as fallback
      const downloadResult = await y2mate.dl(id, 'audio')
      if (downloadResult && isUrl(downloadResult)) {
        await message.send('✅ *Success! Sending audio...*')

        // Try to add metadata if possible
        try {
          const { buffer } = await getBuffer(downloadResult)
          if (buffer) {
            const audioWithMeta = await addAudioMetaData(
              buffer,
              safeTitle,
              safeAuthor,
              '',
              result[0].thumbnail?.url || ''
            )
            return await message.send(audioWithMeta, {
              quoted: message.data,
              mimetype: 'audio/mpeg'
            }, 'audio')
          }
        } catch (metaError) {
          // If metadata fails, send without it
          console.log('Metadata failed for .play command:', metaError.message)
        }

        return await message.sendFromUrl(downloadResult, {
          quoted: message.data,
          mimetype: 'audio/mpeg',
          fileName: `${safeTitle}.mp3`
        })
      }

      throw new Error('No download URL returned')

    } catch (error) {
      console.error('Play Command Error:', error)
      return await message.send(`❌ *Download failed*\n\n💡 *Try:*\n• Different search term\n• Use: \`.ytdl audio ${match}\``)
    }
  }
)
