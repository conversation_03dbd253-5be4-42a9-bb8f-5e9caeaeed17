# 🤖 Complete Levanter Dual Session + YouTube Downloader Setup

## 🎯 **What You Now Have:**

✅ **Enhanced YouTube Downloader Plugin** (`.ytdl` command)  
✅ **Fixed Dual Session Setup** (no more logout issues)  
✅ **Anti-Logout Protection** (2-minute delays between sessions)  
✅ **Session Reset Tools** (for when things go wrong)  
✅ **Complete Configuration** (config.json + config.env)  

---

## 🚀 **Step-by-Step Usage Guide:**

### **If You're Getting Logout Errors:**

```bash
# 1. Reset sessions (cleans corrupted files)
node reset-sessions.js

# 2. WAIT 5 MINUTES (this is critical!)
# Don't start anything for 5 full minutes

# 3. Start with the fixed script
node start-dual-sessions-fixed.js
```

### **If Starting Fresh:**

```bash
# Start the improved dual session manager
node start-dual-sessions-fixed.js
```

### **What Will Happen:**

1. **Session1** starts immediately
2. **Session2** starts after **2 MINUTES** (this prevents logout)
3. You'll see QR codes for each session
4. **IMPORTANT**: Scan them with 2-minute gap between scans
5. Wait for both sessions to fully connect
6. Test with: `.ytdl never gonna give you up auto`

---

## 🎵 **YouTube Downloader Commands:**

### **Video Downloads:**
```
.ytdl https://youtu.be/dQw4w9WgXcQ
.ytdl never gonna give you up
.ytdl jingle bells auto
```

### **Audio Downloads:**
```
.ytdl audio https://youtu.be/dQw4w9WgXcQ
.ytdl audio despacito
.ytdl audio christmas songs auto
```

### **Command Features:**
- **Search**: `.ytdl <search term>` - Shows interactive results
- **Auto Download**: `.ytdl <search term> auto` - Downloads first result
- **Quality Selection**: Automatically tries best quality first
- **Audio Only**: Add `audio` before URL/search term
- **Fallback Methods**: Multiple download sources for reliability

---

## 🔧 **Files Created:**

| File | Purpose |
|------|---------|
| `plugins/ytdl-enhanced.js` | Main YouTube downloader plugin |
| `start-dual-sessions-fixed.js` | Anti-logout dual session starter |
| `reset-sessions.js` | Session reset tool for logout issues |
| `config.env` | Environment configuration with YouTube cookie |
| `fix-logout-issue.js` | One-time fix script (already ran) |

---

## 🚨 **Troubleshooting:**

### **"Intentional Logout" Error:**
```bash
# Stop everything
Ctrl+C

# Reset sessions
node reset-sessions.js

# Wait 5 minutes
# Then restart: node start-dual-sessions-fixed.js
```

### **Plugin Not Working:**
- Make sure both sessions are connected
- Check if `.ytdl` command responds
- Try: `.ytdl audio test auto` for quick test

### **Download Failures:**
- YouTube cookie might be expired
- Try different videos
- Check internet connection

---

## 💡 **Pro Tips:**

### **Preventing Logout Issues:**
- ✅ Always use `start-dual-sessions-fixed.js`
- ✅ Wait 2+ minutes between session starts
- ✅ Don't restart sessions quickly
- ✅ Keep sessions running continuously
- ✅ Use reset script if problems occur

### **Best Download Practices:**
- ✅ Use `auto` for quick downloads
- ✅ Try audio downloads if video fails
- ✅ Use search terms instead of complex URLs
- ✅ Test with popular songs first

---

## 🎉 **Quick Start Commands:**

```bash
# Start your bot (use this one!)
node start-dual-sessions-fixed.js

# If you get logout errors
node reset-sessions.js
# (wait 5 minutes, then start again)

# Test the YouTube plugin
.ytdl never gonna give you up auto
.ytdl audio despacito auto
```

---

## 📊 **Success Indicators:**

✅ **Dual Sessions Working When:**
- Both sessions connect without logout
- Commands work on both sessions
- No "Intentional Logout" errors
- Sessions stay connected after restart

✅ **YouTube Plugin Working When:**
- `.ytdl` command responds
- Search results appear
- Downloads complete successfully
- Both video and audio downloads work

---

## 🆘 **Emergency Reset:**

If everything breaks:

```bash
# 1. Stop all processes
Ctrl+C

# 2. Reset everything
node reset-sessions.js

# 3. Wait 5 minutes (set a timer!)

# 4. Start fresh
node start-dual-sessions-fixed.js

# 5. Scan QR codes with 2-minute gaps

# 6. Test: .ytdl test auto
```

---

**🎊 You're all set! Your enhanced Levanter bot with dual sessions and YouTube downloader is ready to use!**
