{"extra": {"init_session": "[{0}] Initialisation d'une nouvelle session", "load_session": "[{0}] Vérification de l'état de la session", "invalid_session": "[{0}] {1} ID DE SESSION INVALIDE, SCANNER À NOUVEAU !!!", "success_session": "[{0}] Session validée avec succès.", "connecting": "[{0}] Connexion en cours...", "connected": "[{0}] Connecté {1}", "instance_close": "[{0}] Connexion fermée", "instance_restart": "[{0}] Redémarrage de l'instance...", "reconnect": "[{0}] Reconnexion...({1})", "reconnect_after": "[{0}] Reconnexion après 1 minute", "another_login": "[{0}] Session connectée sur un autre appareil.", "error_message": "```---RAPPORT D'ERREUR---\n\nVersion : {0}\nMessage : {1}\nErreur   : {2}\nJid     : {3}\nCommande : {4}\nPlateforme : {5}```\n\n```-----``` *<PERSON><PERSON>é par LyFE avec ❣* ```-----```", "deleted_message": "           messageSupprimé", "install_external_plugin": "[{0}] Installation des plugins externes...", "installed_external_plugin": "[{0}] Plugins externes installés", "plugin_install": "[{0}] Installation des plugins...", "plugin_installed": "[{0}] Installé {1}", "plugins_installed": "[{0}] Plugins installés", "plugin_install_error": "[{0}] Erreur lors de l'installation de {1}, suppression du plugin", "plugin_not_found": "[{0}] Plugin {1} non trouvé (404), suppression du plugin", "group_cmd": "Cette commande est uniquement disponible dans les discussions de groupe."}, "plugins": {"common": {"reply_to_message": "Répondre à un message", "not_admin": "Je ne suis pas administrateur.", "reply_to_image": "Répondre à une image", "update": "_Paramètres mis à jour avec succès ! Vos préférences ont été enregistrées et sont maintenant actives._"}, "menu": {"help": "```╭────────────────╮\n      ʟᴇᴠᴀɴᴛᴇʀ\n╰────────────────╯\n\n╭────────────────\n│ Préfixe : {0}\n│ Utilisateur : {1}\n│ Heure : {2}\n│ Jour : {3}\n│ Date : {4}\n│ Version : {5}\n│ Plugins : {6}\n│ RAM : {7}\n│ Temps d'activité : {8}\n│ Plateforme : {9}\n╰────────────────```", "menu": "```╭═══ LEVANTER ═══⊷\n┃❃╭──────────────\n┃❃│ Préfixe : {0}\n┃❃│ Utilisateur : {1}\n┃❃│ Heure : {2}\n┃❃│ Jour : {3}\n┃❃│ Date : {4}\n┃❃│ Version : {5}\n┃❃│ Plugins : {6}\n┃❃│ RAM : {7}\n┃❃│ Temps d'activité : {8}\n┃❃│ Plateforme : {9}\n┃❃╰───────────────\n╰═════════════════⊷```"}, "afk": {"example": "> *Utilisation de AFK :*\n- Définir AFK : *afk [raison]*\n- Exemple : *afk Je suis occupé* Dernière vue il y a #lastseen\n- L'envoi d'un message supprime automatiquement le statut AFK\n- Désactiver AFK : *afk off*", "not_afk": "Vous n'êtes plus AFK.", "desc": "Définir le statut AFK (Absent du clavier)"}, "alive": {"default": "Je suis en vie\nTemps d'activité : #uptime", "desc": "Afficher le message de statut de vie du bot avec un texte personnalisé optionnel."}, "antifake": {"example": "*Statut Antifake :* {0}\n\n> *Exemples d'utilisation :*\n- *antifake list* - Voir les codes de pays\n- *antifake !91,1* - Autoriser/Interdire des codes de pays spécifiques\n- *antifake on | off* - Activer/Désactiver antifake", "desc": "Activer ou configurer le numéro anti-faux", "not": "Aucun code de pays à lister.", "status": "Antifake est maintenant *{0}*.", "update": "> Antifake mis à jour\n*Autorisé :* {0}\n*Non autorisé :* {1}"}, "antilink": {"desc": "Activer ou configurer l'anti-lien", "disable": "_L'anti-lien est déjà désactivé._", "antilink_notset": "L'anti-lien n'a pas été configuré.", "status": "L'anti-lien est maintenant *{0}*.", "info": "> Statut de l'anti-lien : {0}\n*URLs autorisées :* {1}\n *Action :* {2}", "action_invalid": "*Action spécifiée invalide.*", "action_update": "Action de l'anti-lien mise à jour à : *{0}*", "update": "> Anti-lien mis à jour\n*Autorisé :* {0}\n*Non autorisé :* {1}", "example": "Statut de l'anti-lien : *{0}*\n\n> Exemples d'utilisation :\n- antilink info - Voir les paramètres actuels\n- antilink whatsapp.com - Autoriser des URLs spécifiques\n- antilink on | off - Activer/Désactiver l'anti-lien\n- antilink action/<kick | warn | null> - Définir l'action pour les liens"}, "antiword": {"desc": "Filtrer des mots spécifiques dans le chat de groupe", "example": "Statut AntiWord : {0}\n> *Exemples d'utilisation :*\n- antiword action/<kick | warn | null>* - Définir l'action pour les violations\n- antiword on | off - Activer/Désactiver le filtrage des mots\n- setvar ANTIWORDS:mot1,mot2,... - Définir les mots bloqués", "action_update": "Action AntiWord mise à jour à : *{0}*", "status": "AntiWord est maintenant *{0}*."}, "apk": {"desc": "Télécharger un APK depuis APKMirror", "example": "> *Exemples d'utilisation :*\n- apk Mixplorer\n- apk whatsapp,apkm (inclut les APKs groupés)", "no_result": "_Aucun résultat trouvé pour votre requête._", "apps_list": "Applications correspondantes ({0})"}, "delete": {"desc": "Anti-suppression : Récupérer les messages supprimés", "example": "> *Exemples d'utilisation :*\n- delete p - Envoyer les messages supprimés à votre chat/sudo\n- delete g - Envoyer les messages supprimés dans le même groupe\n- delete off - Désactiver l'anti-suppression\n- delete <jid> - Envoyer les messages supprimés à un JID spécifique", "invalid_jid": "*Erreur :* _J<PERSON> invalide_", "dlt_msg_jid": " _Les messages supprimés seront envoyés à : {0}_", "dlt_msg_disable": "L'anti-suppression a été désactivée.", "dlt_msg_sudo": "_Les messages supprimés seront envoyés à votre chat ou sudo._", "dlt_msg_chat": "_Les messages supprimés seront envoyés dans le chat lui-même._"}, "dlt": {"desc": "Supprimer les messages auxquels on répond"}, "fb": {"desc": "Télécharger une vidéo Facebook", "example": "", "quality": "Choisir la qualité de la vidéo", "invalid": "*Erreur :* _Aucune vidéo trouvée pour l'URL donnée._"}, "fancy": {"desc": "<PERSON><PERSON><PERSON> un texte stylisé à partir du texte donné", "example": "> *Utilisation :*\nfancy <texte>\nfancy <numéro_de_police> (répondre à un message)\n\n*Exemple :*\n- fancy Bonjour\n- fancy 7 (en répondant à un message)", "invalid": "*Numéro de police invalide !*\nVeuillez entrer un numéro entre *1-47*."}, "stop": {"desc": "Supprimer les filtres dans le chat", "example": "> *Utilisation :*\n- stop <filtre>\n- stop salut", "delete": "{0} supprimé", "not_found": "_{0} non trouvé dans les filtres._"}, "filter": {"desc": "G<PERSON>rer les filtres dans les groupes", "example": "> *Exemple :*\n- filter salut (en répondant à un message texte)\n- filter list (Affiche les filtres actuels)", "list": "> *Filtres actuels :*\n{0}", "filter_add": "Filtre *{0}* ajouté"}, "forward": {"desc": "Transférer le message répondu à des JID(s) spécifiés", "foward": "Message transféré à : {0}", "example": "JID invalide !\n> *Utilisation :*\n- forward <jid>\n- forward <EMAIL>"}, "save": {"desc": "Transférer le message répondu à vous-même", "save": "Message sauvegardé !"}, "gemini": {"desc": "Google Gemini AI - Posez n'importe quelle question !", "example": "> *Exemple :*\n- gemini salut\n- gemini qu'y a-t-il sur la photo (en répondant à une image)", "Key": "> Clé API Gemini manquante !\nObtenez-en une sur : https://aistudio.google.com/app/apikey\n\n*Définissez-la avec :*\nsetvar GEMINI_API_KEY = votre_clé_api"}, "gstop": {"desc": "Supprimer les gfiltres dans tous les groupes", "example": "> *Utilisation :*\n- gstop <filtre>\n- gstop salut", "delete": "{0} supprimé", "not_found": "_{0} non trouvé dans les gfiltres._"}, "pstop": {"desc": "Supprimer les pfiltres dans tous les groupes", "example": "> *Utilisation :*\n- pstop <filtre>\n- pstop salut", "delete": "{0} supprimé", "not_found": "_{0} non trouvé dans les pfiltres._"}, "gfilter": {"desc": "Gérer les filtres globaux dans les groupes", "example": "> *Exemple :*\n- gfilter salut (en répondant à un message texte)\n- gfilter list (Affiche les gfiltres actuels)", "add": "Gfiltre *{0}* ajouté"}, "pfilter": {"desc": "G<PERSON>rer les filtres globaux dans les chats personnels", "example": "> *Exemple :*\n- pfilter salut (en répondant à un message texte)\n- pfilter list (Affiche les pfiltres actuels)", "add": "Pfiltre *{0}* ajouté"}, "gpp": {"desc": "Changer l'icône du groupe", "update": "_Icône du groupe mise à jour_"}, "greet": {"setdesc": "Définir un message de salutation personnalisé", "setexample": "> *Exemple :* set<PERSON><PERSON>, ceci est un bot. Mon patron répondra sous peu.", "setupdate": "_Message de salutation mis à jour._", "getdesc": "Récupérer le message de salutation personnalisé", "notsetgreet": "> Aucun message de salutation n'a été défini.", "deldesc": "Supprimer le message de salutation personnalisé", "delupdate": "Message de salutation supprimé."}, "greetings": {"welcome_desc": "Envoyer un message de bienvenue aux nouveaux membres", "welcome_example": "Le bienvenue est actuellement {0}\n\nPour plus de détails, visitez : https://levanter-plugins.vercel.app/faq", "welcome_enable": "_Le bienvenue est maintenant activé_", "welcome_disable": "_Le bienvenue est maintenant désactivé_", "welcome_delete": "_Message de bienvenue supprimé_", "goodbye_desc": "Envoyer un message d'au revoir aux membres", "goodbye_example": "L'au revoir est actuellement {0}\n\nPour plus de détails, visitez : https://levanter-plugins.vercel.app/faq", "goodbye_enable": "_L'au revoir est maintenant activé_", "goodbye_disable": "_L'au revoir est maintenant désactivé_", "goodbye_delete": "_Message d'au revoir supprimé_"}, "groq": {"example": "*Exemple :* groq <PERSON>ut\n\nVous pouvez éventuellement définir les variables d'environnement suivantes :\n- GROQ_API_KEY\n- GROQ_MODEL\n- GROQ_SYSTEM_MSG\n\nPour plus de détails, visitez : https://console.groq.com/keys", "desc": "Interagir avec GROQ AI"}, "kick": {"desc": "Retirer des membres du groupe.", "not_admin": "Je ne suis pas administrateur, donc je ne peux pas retirer des membres.", "mention_user": "Veuillez mentionner un utilisateur ou répondre à son message.", "admin": "L'utilisateur spécifié est un administrateur et ne peut pas être retiré.", "kicking_all": "Retrait de tous les membres non-administrateurs... ({0} membres). Redémarrez le bot si vous voulez arrêter."}, "add": {"desc": "Ajouter un membre aux groupes", "warning": "> É<PERSON><PERSON><PERSON> d'ajouter des numéros qui ne sont pas enregistrés dans les contacts ; cela peut augmenter le risque d'être banni.", "not_admin": "Je ne suis pas administrateur, donc je ne peux pas ajouter des membres.", "invalid_number": "Veuillez fournir un numéro de téléphone valide. Exemple : add 91987654321", "failed": "Échec de l'ajout. Une invitation a été envoyée à la place."}, "promote": {"desc": "Donner un rôle d'administrateur", "not_admin": "Je ne suis pas administrateur, donc je ne peux pas modifier les rôles.", "mention_user": "V Basculer mentionner un utilisateur ou répondre à son message.", "already_admin": "L'utilisateur est déjà administrateur."}, "demote": {"desc": "<PERSON><PERSON><PERSON> le rôle d'administrateur", "not_admin": "Je ne suis pas administrateur, donc je ne peux pas modifier les rôles.", "mention_user": "Veuillez mentionner un utilisateur ou répondre à son message.", "not_admin_user": "L'utilisateur n'est pas administrateur."}, "invite": {"desc": "Obtenir un lien d'invitation de groupe", "not_admin": "Je ne suis pas administrateur, donc je ne peux pas générer un lien d'invitation.", "success": "Voici le lien d'invitation du groupe :\n{0}"}, "mute": {"desc": "Rendre les groupes réservés aux administrateurs uniquement", "not_admin": "Je ne suis pas administrateur, donc je ne peux pas modifier les paramètres du groupe.", "mute": "Muet pendant {0} minutes."}, "unmute": {"desc": "Permettre à tous les participants d'envoyer des messages dans le groupe", "not_admin": "Je ne suis pas administrateur, donc je ne peux pas modifier les paramètres du groupe."}, "join": {"desc": "Rejoindre des groupes avec un lien d'invitation", "invalid_link": "Veuillez fournir un lien d'invitation de groupe WhatsApp valide.", "group_full": "Le groupe est complet et ne peut pas accepter de nouveaux membres.", "success": "Groupe rejoint avec succès.", "request_sent": "<PERSON><PERSON><PERSON> de rejoindre envoyée."}, "revoke": {"desc": "Révoquer le lien d'invitation du groupe", "not_admin": "Je ne suis pas administrateur, donc je ne peux pas révoquer le lien d'invitation."}, "group_info": {"desc": "Afficher les informations du lien d'invitation du groupe", "invalid_link": "Veuillez fournir un lien d'invitation WhatsApp valide.", "details": "*Nom :* {0}\n*ID du groupe :* {1}@g.us\n*<PERSON>prié<PERSON> :* {2}\n*Membres :* {3}\n*<PERSON><PERSON><PERSON> le :* {4}\n*Description :* {5}"}, "common_members": {"desc": "Afficher ou expulser les membres communs dans deux groupes ou plus", "found": "0 membres communs trouvés."}, "insta": {"usage": "Exemple : insta <URL Instagram>", "not_found": "Non trouvé.", "desc": "Télécharger des publications, reels et vidéos Instagram."}, "ison": {"usage": "Exemple : ison <numéro de téléphone>", "not_exist": "`*N'existe pas sur WhatsApp* ({0})\n`", "exist": "\n*Existe sur WhatsApp* ({0})\n", "privacy": "*Paramètres de confidentialité activés* ({0})\n", "desc": "Vérifier si un numéro de téléphone est enregistré sur WhatsApp."}, "lydia": {"usage": "Utilisation : lydia on | off\nRépondre ou mentionner pour activer pour un utilisateur spécifique.", "activated": "<PERSON> a été activée.", "deactivated": "<PERSON> a été désactivée.", "note": "<PERSON><PERSON> ne fonctionne qu'à partir d'un message en réponse.", "desc": "<PERSON>r ou désactiver la fonctionnalité de chatbot."}, "rotate": {"usage": "Exemple : rotate right|left|flip (répondre à une vidéo).", "not_found": "Veuillez répondre à une vidéo et spécifier une direction de rotation valide (droite, gauche ou retourner).", "desc": "Faire pivoter une vidéo vers la droite, la gauche ou la retourner.", "convert": "_Conversion en cours..._"}, "mp3": {"usage": "Répondre à une vidéo ou un audio pour le convertir en MP3.", "not_found": "Veuillez répondre à un message vidéo ou audio.", "desc": "Convertir une vidéo en audio ou un clip audio en note vocale."}, "photo": {"usage": "Répondre à un autocollant photo pour le convertir en image.", "desc": "Convertir un autocollant en image."}, "reverse": {"usage": "Répondre à une vidéo ou un audio pour inverser sa lecture.", "not_found": "Veuillez répondre à un message vidéo ou audio.", "desc": "Inverser la lecture d'une vidéo ou d'un clip audio."}, "cut": {"usage": "Exemple : cut 0;30 (d<PERSON><PERSON>;du<PERSON><PERSON>) (répo<PERSON><PERSON> à une vidéo ou un audio).", "not_found": "Veuillez répondre à une vidéo ou un audio avec des valeurs de début et de durée valides (par ex., 10;30).", "desc": "Couper un segment d'un fichier audio ou vidéo."}, "trim": {"usage": "Exemple : trim 10;30 (r<PERSON><PERSON><PERSON><PERSON> à une vidéo).", "not_found": "Veuillez répondre à une vidéo avec des valeurs de début et de durée valides (par ex., 60;30).", "desc": "<PERSON><PERSON>ner une vidéo entre les temps de début et de durée spécifiés."}, "page": {"usage": "Exemple : page 1 (répondre à une image).", "not_found": "Veuillez répondre à une image avec une légende numérique indiquant le numéro de page.", "desc": "Ajouter une image en tant que page à un document PDF.", "add": "Page {0} a<PERSON><PERSON>e !"}, "pdf": {"usage": "Exemple : pdf note (fournir un titre pour le PDF).", "not_found": "Veuillez fournir un titre pour le document PDF.", "desc": "Convertir des images en un document PDF."}, "merge": {"usage": "Exemple : merge 1 (répondre avec un numéro d'ordre à une vidéo).", "not_found": "Veuillez répondre à une vidéo avec un numéro d'ordre valide.", "desc": "Fusionner plusieurs vidéos en une seule.", "merge": "_Fusion de {0} vidéos_", "add": "_Vidéo {0} ajou<PERSON>e_"}, "compress": {"usage": "Répondre à une vidéo pour la compresser.", "desc": "Compresser un fichier vidéo pour réduire sa taille."}, "bass": {"usage": "Exemple : bass 10 (r<PERSON><PERSON><PERSON><PERSON> à un audio ou une vidéo).", "desc": "Modifier les niveaux de basses d'un fichier audio."}, "treble": {"usage": "Exemple : treble 10 (répondre à un audio ou une vidéo).", "desc": "Modifier les niveaux d'aigus d'un fichier audio."}, "histo": {"usage": "Répondre à un audio ou une vidéo pour générer une vidéo d'histogramme.", "desc": "Convertir l'audio en une vidéo d'histogramme visuel."}, "vector": {"usage": "Répondre à un audio ou une vidéo pour créer une vidéo de visualisation vectorielle.", "desc": "Convertir l'audio en une vidéo de visualisation vectorielle."}, "crop": {"usage": "Exemple : crop 512,512,0,512 (répond<PERSON> à une vidéo).", "not_found": "Veuillez répondre à une vidéo avec des dimensions de recadrage valides au format : out_w,out_h,x,y.", "desc": "Recadrer une vidéo aux dimensions spécifiées.", "xcrop": "Largeur de la vidéo : *{0}*, hauteur : *{1}*\n<PERSON><PERSON><PERSON>z une taille de sortie entre les deux."}, "low": {"usage": "Répondre à un audio ou une vidéo pour baisser la tonalité.", "desc": "Modifier la hauteur de l'audio vers une tonalité plus basse."}, "pitch": {"usage": "Répondre à un audio ou une vidéo pour ajuster la hauteur.", "not_found": "Veuillez répondre à un message audio ou vidéo.", "desc": "Ajuster la hauteur d'un fichier audio."}, "avec": {"usage": "Répondre à un audio ou une vidéo pour le convertir en format vidéo.", "not_found": "Veuillez répondre à un message audio ou vidéo.", "desc": "Convertir un clip audio en vidéo."}, "avm": {"usage": "Répondre avec un audio et une vidéo pour les fusionner.", "desc": "Fusionner des fichiers audio et vidéo en un seul.", "audio_add": "_Audio ajouté !_", "video_add": "_Vid<PERSON><PERSON> a<PERSON> !_"}, "black": {"usage": "Répondre à un audio ou une vidéo pour générer une vidéo avec un fond noir.", "desc": "Convertir un clip audio en une vidéo avec un fond noir."}, "mediafire": {"usage": "Exemple : mediafire <URL Mediafire>", "not_found": "Fichier non trouvé. Veuillez vérifier l'URL et réessayer.", "desc": "Télécharger un fichier depuis Mediafire."}, "mention": {"usage": "Exemple : mention on | off | get\n(Répondre à un message pour cibler un utilisateur spécifique.)", "desc": "Configurer et gérer la fonctionnalité de mention pour des réponses automatisées.", "not_activated": "Réponse à la mention non activée.", "current_status": "La mention est {0}. <PERSON><PERSON> plus de détails, visitez : https://levanter-plugins.vercel.app/faq", "activated": "Réponse à la mention activée.", "deactivated": "Réponse à la mention désactivée.", "updated": "Paramètres de mention mis à jour."}, "status": {"usage": "Utilisation : status on | off | no-dl | except-view <jid,...> | only-view <jid,...>", "desc": "Gérer automatiquement la visualisation des statuts WhatsApp."}, "call": {"usage": "Utilisation : call on | off", "desc": "Rejeter automatiquement les appels entrants."}, "read": {"usage": "Utilisation : read on | off", "desc": "<PERSON><PERSON> ou désactiver la lecture automatique des messages entrants."}, "online": {"usage": "Utilisation : online on | off", "desc": "Maintenir le statut de votre compte toujours en ligne."}, "movie": {"usage": "Exemple : movie <titre du film>", "not_found": "Film non trouvé. Veuillez vérifier le titre et réessayer.", "desc": "Récupérer des informations détaillées sur un film, y compris l'intrigue complète, depuis l'API OMDB."}, "msgs": {"desc": "Afficher le nombre de messages du groupe pour chaque membre, y compris les totaux individuels et la durée depuis la dernière vue.", "msg_init": "\n*Numéro :* {0}\n*Nom :* {1}\n*Total des messages :* {2}\n", "msg_last": "*Dernière vue :* il y a {0}\n"}, "reset": {"usage": "Exemple : reset all OU reset <répondre/mentionner>", "desc": "Réinitialiser le compteur de messages pour l'ensemble du groupe ou un membre spécifique.", "reset_all": "Les compteurs de messages de tout le monde ont été supprimés", "reset_one": "_Les compteurs de messages de @{0} ont été supprimés._"}, "inactive": {"usage": "> Exemples :\n- inactive day 10\n- inactive day 10 kick\n- inactive total 100\n- inactive total 100 kick\n- inactive day 7 total 150\n- inactive day 7 total 150 kick\n\nSi 'kick' n'est pas <PERSON>n<PERSON>, juste lister", "desc": "Identifier ou supprimer les membres inactifs en fonction du nombre de messages ou de la durée d'inactivité. Ajoutez 'kick' pour supprimer les membres.", "inactives": "_Total des inactifs : {0}_", "removing": "_Suppression de {0} membres inactifs dans 7 secondes_"}, "amute": {"usage": "Utilisation : amute <heure> <min>\n- amute on | off\n- amute info\n\nRépondre avec un texte pour définir un message de mise en sourdine", "desc": "Programmer une mise en sourdine automatique du groupe à une heure spécifiée avec un message personnalisé optionnel.", "not_found": "Paramètres AutoMute non trouvés.", "already_disabled": "AutoMute déjà désactivé.", "enabled": "AutoMute activé.", "disabled": "AutoMute désactivé.", "invalid_format": "> Exemple :\n- amute 6 0\n- amute on | off\n- amute info\n\nRépondre avec un texte pour définir un message de mise en sourdine.", "scheduled": "Le groupe sera mis en sourdine à {0}\n*message :* {1}", "info": "Heure : {0}\nMinute : {1}\nTemps : {2}\nSourdine : {3}\nMessage : {4}"}, "aunmute": {"usage": "Utilisation : aunmute <heure> <min>\n- aunmute on | off\n- aunmute info\nRépondre avec un texte pour définir un message de réactivation", "desc": "Programmer une réactivation automatique du groupe à une heure spécifiée avec un message personnalisé optionnel.", "not_found": "Paramètres AutoUnMute non trouvés.", "already_disabled": "AutoUnMute déjà désactivé.", "enabled": "AutoUnMute activé.", "disabled": "AutoUnMute désactivé.", "invalid_format": "> Exemple :\n- aunmute 16 30\n- aunmute on | off\n- aunmute info\n\nRépondre avec un texte pour définir un message de réactivation.", "scheduled": "Le groupe sera réactivé à {0}\n*message :* {1}", "info": "Heure : {0}\nMinute : {1}\nTemps : {2}\nSourdine : {3}\nMessage : {4}"}, "zushi": {"usage": "> Exemple :\n- zushi ping, sticker\n- Pour définir toutes les commandes, tapez 'list' puis répondez avec le message copié (par ex., zushi message_copié).", "desc": "Permet d'activer des commandes spécifiques pour une utilisation par d'autres dans le chat.", "already_set": "{0} est déjà configuré.", "allowed": "*commandes autorisées pour* @{0}\n{1}"}, "yami": {"usage": "Utilisez simplement : yami", "desc": "Affiche la liste des commandes actuellement autorisées dans ce chat.", "not_set": "Aucune commande autorisée n'a encore été définie."}, "ope": {"usage": "Exemple : ope ping, sticker OU ope all", "desc": "Supprime ou désactive les commandes autorisées spécifiées.", "not_found": "Aucune commande autorisée trouvée pour {0}.", "all_removed": "Toutes les commandes autorisées ont été supprimées.", "removed": "*commandes supprimées pour* @{0}\n{1}"}, "pdm": {"usage": "Utilisation : pdm on | off", "desc": "<PERSON>r ou désactiver les notifications automatiques pour les événements de promotion/rétrogradation dans le groupe.", "not_found": "Veuillez spécifier 'on' ou 'off'. Par exemple : pdm on", "activated": "Alerte de promotion/rétrogradation activée.", "deactivated": "Alerte de promotion/rétrogradation désactivée."}, "ping": {"desc": "Vérifier le temps de réponse du bot (latence).", "ping_sent": "Ping !", "pong": "Pong ! Temps de réponse : {0} ms"}, "pinterest": {"usage": "Exemple : pinterest <URL Pinterest>", "not_found": "Aucun média trouvé. Veuillez vérifier l'URL et réessayer.", "desc": "Télécharger des vidéos ou images Pinterest."}, "plugin": {"usage": "> Exemple :\n- plugin <URL Gist>\n- plugin list", "desc": "Installer des plugins externes en fournissant une URL Gist contenant le code du plugin, ou lister tous les plugins installés.", "invalid": "Veuillez fournir une URL de plugin valide ou un nom de plugin.", "not_installed": "Aucun plugin n'est actuellement installé.", "installed": "Nouveaux plugins installés : {0}"}, "remove": {"usage": "> Exemple :\n- remove <nom_du_plugin>\n- remove all", "desc": "Supprimer des plugins externes en spécifiant le nom du plugin ou supprimer tous les plugins installés.", "not_found": "Plugin *{0}* non trouvé.", "removed": "Les plugins ont été supprimés avec succès."}, "reboot": {"desc": "Redémarrer l'instance du bot en utilisant PM2.", "starting": "Redémarrage en cours..."}, "fullpp": {"usage": "Exemple : fullpp (répondre à une image)", "desc": "Définir une photo de profil en taille réelle.", "updated": "Photo de profil mise à jour."}, "jid": {"desc": "Re<PERSON>ne le JID d'un utilisateur ou d'un chat. <PERSON><PERSON><PERSON><PERSON> s'il y a un utilisateur mentionné, un message en réponse, ou utilise par défaut le JID du chat actuel."}, "left": {"desc": "<PERSON><PERSON><PERSON> le groupe actuel. Si un texte supplémentaire est fourni, il sera envoyé avant de quitter."}, "block": {"usage": "Exemple : block (r<PERSON><PERSON><PERSON><PERSON> à ou mentionner un utilisateur)", "desc": "Bloque l'utilisateur spécifié.", "status": "<PERSON><PERSON><PERSON><PERSON>"}, "unblock": {"usage": "Exemple : unblock (r<PERSON><PERSON><PERSON><PERSON> à ou mentionner un utilisateur)", "desc": "Débloque l'utilisateur spécifié.", "status": "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_"}, "pp": {"usage": "Exemple : pp (répondre à une image)", "desc": "Met à jour votre photo de profil en utilisant l'image du message répondu."}, "whois": {"number": "*Numéro :* {0}", "name": "*Nom :* {0}", "about": "*À propos :* {0}", "setAt": "*<PERSON><PERSON><PERSON>i le :* {0}", "owner": "*Pro<PERSON><PERSON>é<PERSON> :* {0}", "members": "*Membres* : {0}", "description": "*Description* : {0}", "created": "*Créé* : {0}", "usage": "Exemple : whois <jid ou identifiant d'utilisateur>", "desc": "Affiche la photo de profil et des informations supplémentaires (par ex., à propos, statut) d'un utilisateur ou d'un groupe."}, "gjid": {"desc": "Liste tous les JIDs des groupes avec leurs noms de groupe."}, "qr": {"usage": "Exemple : qr test OU répondre à une image QR avec qr", "desc": "Générer un code QR à partir du texte fourni ou décoder un code QR à partir d'une image répondue."}, "reddit": {"usage": "Exemple : reddit <URL>", "desc": "Télécharge une vidéo à partir de la publication Reddit spécifiée en utilisant l'URL fournie.", "error": "Aucune vidéo trouvée pour l'URL donnée."}, "rmbg": {"usage": "Exemple : rmbg (répondre à une image)", "desc": "Supprime l'arrière-plan de l'image répondue en utilisant l'API remove.bg", "key": "Pour utiliser cette commande, assurez-vous de vous être inscrit sur remove.bg, d'avoir vérifié votre compte, d'avoir copié votre clé API et de l'avoir définie avec .setvar RMBG_KEY:<votre_clé_api> (par ex., .setvar RMBG_KEY:GWQ6jVy9MBpfYF9SnyG8jz8P). INSCRIPTION : https://accounts.kaleido.ai/users/sign_up | CLÉ API : https://www.remove.bg/dashboard#api-key", "error": "Clé API ou image manquante. Veuillez définir votre clé API et répondre à une image."}, "setschedule": {"usage": "> *Exemple :*\n- setschedule jid,min-heure-jour-mois (au format 24 heures, jour et mois optionnels)\n- setschedule <EMAIL>, 9-9-13-8\n- setschedule <EMAIL>, 0-10 (envoyer un message quotidiennement à 10h)\n- setschedule <EMAIL>, 0-10, once (envoyer un message à 10h, une seule fois)", "desc": "Programmer un message à envoyer automatiquement à une heure spécifiée. Fournissez le(s) JID(s) cible(s) et l'heure (au format minutes-heure-jour-mois ; jour et mois sont optionnels). Répondez au message que vous souhaitez programmer.", "invalid": "Format ou heure de programmation invalide. Veuillez suivre l'un des exemples fournis.", "no_reply": "Veuillez répondre au message que vous souhaitez programmer pour l'envoi.", "scheduled": "_Programmé avec succès pour envoyer à_ *{0}* _dans_ @{1}."}, "getschedule": {"desc": "Récupérer tous les messages programmés pour le chat spécifié.", "not_found": "Il n'y a pas de messages programmés.", "time": "Heure : {0}"}, "delschedule": {"usage": "> Exemple :\n- delschedule <EMAIL>, 8-8-10-10\n- delschedule <EMAIL>\n- delschedule all", "desc": "Supprimer un message programmé en spécifiant le JID cible et l'heure, ou supprimer tous les messages programmés.", "invalid": "Format invalide. Veuillez suivre l'un des exemples.", "not_found": "Programmation non trouvée.", "deleted": "_Programmation supprimée._"}, "setstatus": {"usage": "Exemple : setstatus jid,jid,jid,... OU setstatus contact", "desc": "Définir un statut WhatsApp pour des contacts spécifiques ou pour des contacts importés. Répondez à un message (texte, image ou vidéo) pour définir le statut.", "reply_required": "Veuillez répondre à un message pour définir le statut.", "sent": "_Statut envoyé à {0} contacts._"}, "scstatus": {"usage": "Exemples :\n- scstatus jid,jid,jid,...|min-heure-jour-mois (jour et mois optionnels)\n- scstatus contact|min-heure-jour-mois\n- scstatus delete all|min-heure-jour-mois\n- scstatus list", "desc": "Programmer un statut WhatsApp à envoyer à une heure spécifiée. Utilisez 'contact' pour les contacts importés ou fournissez des JIDs spécifiques.", "reply_required": "Veuillez répondre à un message pour programmer le statut.", "invalid": "Format invalide. Veuillez suivre l'un des exemples fournis.", "scheduled": "_Programmé avec succès pour envoyer à_ {0}.", "list": "Liste des statuts programmés :", "deleted": "Programmation supprimée."}, "antispam": {"usage": "Utilisation : antispam on | off", "desc": "<PERSON>r ou désactiver la fonctionnalité AntiSpam pour le groupe.", "activated": "AntiSpam activé.", "deactivated": "AntiSpam désactivé."}, "sticker": {"desc": "Convertir une image ou une vidéo en autocollant. Répondez à un message image ou vidéo pour générer un autocollant.", "reply_required": "Veuillez répondre à une image ou une vidéo."}, "circle": {"desc": "Convertir une image en autocollant circulaire.", "reply_required": "Veuillez répondre à une image."}, "take": {"usage": "Exemple : take <titre,artistes,url> (répondre à un autocollant ou un audio). Pour l'audio, le titre est requis ; les artistes et l'URL sont optionnels.", "desc": "Modifier le pack d'autocollants en mettant à jour ses métadonnées. Si vous répondez à un autocollant, mettez à jour ses métadonnées de pack. Si vous répondez à un audio, ajoutez des métadonnées (titre, artistes, URL) au fichier.", "reply_required": "Veuillez répondre à un message autocollant ou audio.", "additional_info": "Pour les métadonnées audio, les artistes ou l'URL sont optionnels."}, "mp4": {"desc": "Convertir un autocollant animé (WebP) en vidéo MP4.", "reply_required": "Veuillez répondre à un autocollant animé."}, "story": {"usage": "Exemple : story <nom d'utilisateur> (ou répondre à un message contenant le nom d'utilisateur)", "desc": "Télécharger les stories Instagram pour le nom d'utilisateur spécifié. Si plusieurs stories sont disponibles, une liste sera fournie pour la sélection.", "not_found": "Aucune story trouvée pour le nom d'utilisateur fourni.", "list": "Total de {0} stories disponibles. Veuillez en sélectionner une à télécharger.\n"}, "tag": {"usage": "Exemple : tag all | tag admin | tag notadmin | tag <message personnalisé> (ou répondre à un message avec 'tag')", "desc": "Mentionner les membres du groupe selon votre choix. Utilisez 'all' pour mentionner tous les membres, 'admin' pour mentionner uniquement les administrateurs du groupe, 'notadmin' pour mentionner les membres non-administrateurs, ou fournissez un message personnalisé à envoyer avec les mentions."}, "tictactoe": {"usage": "Exemple : tictactoe <jid_opposant> OU tictactoe restart <jid_opposant> OU tictactoe end", "desc": "Jouer à un jeu de morpion contre un adversaire. Défiez un utilisateur en le mentionnant, en répondant à son message ou en spécifiant son JID. Utilisez 'tictactoe end' pour terminer le jeu et 'tictactoe restart <jid_opposant>' pour recommencer avec un nouvel adversaire.", "choose_opponent": "Veuillez choisir un adversaire en répondant à un message ou en mentionnant un utilisateur. Vous ne pouvez pas jouer contre vous-même.", "game_ended": "<PERSON><PERSON> termin<PERSON>.", "game_restarted": "<PERSON><PERSON> <PERSON>é<PERSON> avec le nouvel adversaire.", "invalid_input": "Entrée invalide. Veuillez fournir un adversaire valide ou utiliser 'end' ou 'restart' de manière appropriée.", "players": "<PERSON><PERSON><PERSON>", "already_occupied": "_<PERSON><PERSON><PERSON><PERSON> occupé_", "current_player": "<PERSON><PERSON><PERSON> actuel", "game_finish": "Fin du jeu 🏁", "winner": "<PERSON><PERSON><PERSON>"}, "tiktok": {"usage": "Utilisation : tiktok <URL TikTok> (ou répondre à un message avec l'URL)", "desc": "Télécharger une vidéo TikTok à partir de l'URL fournie.", "not_found": "Vidéo non trouvée. Veuillez vérifier l'URL et réessayer."}, "tog": {"usage": "Exemple : tog ping off", "desc": "<PERSON>r ou dés<PERSON>r une commande du bot.", "invalid": "Entrée invalide. Utilisez : tog <commande> on|off (par ex., tog ping off)", "self_reference": "Vouliez-vous vraiment me tuer ?", "enabled": "{0} Activé.", "disabled": "{0} Désactivé."}, "trt": {"usage": "Exemple : trt ml salut OU trt ml (répondre à un message texte)", "desc": "Traduire un texte en utilisant Google Translate. Spécifiez le code de la langue cible (et éventuellement le code de la langue source) en répondant à un message."}, "twitter": {"usage": "Exemple : twitter <URL Twitter> (ou répondre à un message contenant l'URL)", "desc": "Télécharger une vidéo Twitter. Si plusieurs options de qualité sont disponibles, vous serez invité à en choisir une.", "not_found": "Aucune vidéo trouvée pour l'URL Twitter fournie.", "choose_quality": "> Choisir la qualité de la vidéo\n"}, "upload": {"usage": "Exemple : upload <URL> (ou répondre à un message contenant une URL)", "desc": "Télécharger des médias à partir d'une URL fournie. Pour les URL raccourcies de Google Images, l'URL directe de l'image est récupérée automatiquement."}, "url": {"usage": "Exemple : url OU url imgur (répondre à une image ou une vidéo)", "desc": "Convertir une image ou une vidéo en URL. Spécifiez éventuellement un paramètre (par ex., 'imgur') pour obtenir l'URL à partir d'un service spécifique."}, "getvar": {"usage": "Exemple : getvar sudo", "desc": "Afficher la valeur d'une variable. Fournissez la clé de la variable (insensible à la casse) pour récupérer sa valeur.", "not_found": "{0} non trouvé dans les variables."}, "delvar": {"usage": "Exemple : delvar sudo", "desc": "Supprimer une variable en spécifiant sa clé.", "not_found": "{0} non trouvé dans les variables.", "deleted": "{0} supprimé."}, "setvar": {"usage": "Exemple : setvar clé = valeur", "desc": "Définir une variable avec une clé et une valeur spécifiques. Utilisez '=' pour séparer la clé de la valeur.", "success": "Nouvelle variable {0} ajou<PERSON>e en tant que {1}."}, "allvar": {"desc": "<PERSON>ff<PERSON><PERSON> toutes les variables stockées dans l'ordre trié."}, "vote": {"usage": "> Exemple :\nvote q|Quelle est votre couleur préférée ?\no|😀|Bleu\no|😊|Rouge", "desc": "Lancer un vote dans un groupe WhatsApp.", "notes": "Si aucun destinataire n'est spécifié, le message de vote sera simplement envoyé au groupe actuel.", "no_vote": "Aucun vote disponible !", "total_vote": "Total des votes : *{0}*", "delete_vote": "Supprimez le vote actuel pour en définir un nouveau.", "option_required": "Deux options ou plus requises", "question_required": "Question requise", "vote": "R<PERSON><PERSON><PERSON><PERSON> ou répondez à une option pour voter.", "vote_deleted": "_Vote supprimé._", "voted": "@{0} a voté pour {1}\n\n${2}"}, "warn": {"usage": "Exemple : warn @utilisateur OU warn reset @utilisateur. Vous pouvez également répondre au message d'un utilisateur et taper 'warn' ou 'warn reset'.", "desc": "Avertir un utilisateur dans le chat de groupe. Cette commande augmente le compteur d'avertissements d'un utilisateur. Si le compteur dépasse la limite, l'utilisateur sera expulsé du groupe. Utilisez 'warn reset' pour effacer les avertissements d'un utilisateur.", "reset_usage": "Exemple : warn reset @utilisateur (ou répondre au message d'un utilisateur et taper 'warn reset').", "cannot_remove_admin": "Je ne peux pas retirer un administrateur."}, "wcg": {"usage": "Exemples :\n- wcg start (forcer le démarrage du jeu de chaîne de mots)\n- wcg end (terminer le jeu actuel)\n- wcg <mot> (jouer en continuant la chaîne de mots)", "desc": "Jeu de chaîne de mots : Participez en fournissant un mot qui continue la chaîne. Utilisez 'wcg start' pour forcer le démarrage d'un nouveau jeu ou 'wcg end' pour arrêter le jeu actuel."}, "wrg": {"usage": "Exemples :\n- wrg start (forcer le démarrage du jeu de mots aléatoires)\n- wrg end (terminer le jeu actuel)\n- wrg <mot> (jouer en soumettant un mot)", "desc": "Jeu de mots aléatoires : Participez à un jeu où vous soumettez des mots en réponse à une invite aléatoire. Utilisez 'wrg start' pour commencer un nouveau jeu ou 'wrg end' pour terminer le jeu."}, "yts": {"usage": "Exemple : yts baymax", "desc": "Rechercher des vidéos YouTube par requête ou URL. Si une URL YouTube valide est fournie, des informations détaillées pour la première vidéo sont retournées."}, "song": {"usage": "Exemple : song indila love story OU song <URL YouTube> (répondre à un message est également pris en charge)", "desc": "Télécharger une chanson depuis YouTube. Si une URL est fournie, la chanson est téléchargée directement ; sinon, une recherche est effectuée et une liste de résultats est générée pour la sélection.", "not_found": "Aucune chanson trouvée pour la requête ou l'URL fournie."}, "video": {"usage": "Exemple : video <URL YouTube> (ou répondre à un message avec l'URL)", "desc": "Télécharger une vidéo YouTube. Si une URL directe est fournie, la vidéo est téléchargée ; si une requête de recherche est fournie, une liste de résultats est générée pour la sélection.", "not_found": "Aucune vidéo trouvée pour la requête ou l'URL fournie."}, "update": {"usage": "Exemple : update", "desc": "Vérifier les nouvelles mises à jour. Affiche les mises à jour disponibles ou confirme que le bot est à jour.", "up_to_date": "Le bot est déjà à jour.", "available": "{0} nouvelles mises à jour disponibles :\n{1}"}, "update_now": {"usage": "Exemple : update now", "desc": "Mettre à jour le bot vers la dernière version.", "up_to_date": "Le bot est déjà à jour. Aucune mise à jour disponible.", "updating": "Mise à jour du bot en cours...", "updated": "Le bot a été mis à jour avec succès !"}, "antigm": {"usage": "> Usage:\n- antigm <on|off> — Enable or disable anti group mention\n- antigm <delete|warn|kick> — Set action when someone mentions the group\n- antigm ignore <jid,jid,jid,...> — Ignore anti group mention from specific groups", "action": "Action updated: group mentions will now result in *{0}*.", "filter": "Ignore JIDs updated for anti group mention.", "enabled": "Anti group mention has been enabled.", "disabled": "Anti group mention has been disabled."}}}