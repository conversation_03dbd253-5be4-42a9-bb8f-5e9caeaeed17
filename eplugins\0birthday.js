const { bot, textMaker } = require('../lib')

bot(

	{		pattern: 'birthday ?(.*)',

		fromMe: true,

		desc: 'happy birthday',

		type: 'textmaker',

	},

	async (message, match) => {

		if (!match) return await message.sendMessage('Give me text')

		const effect_url =

			'https://en.ephoto360.com/write-name-on-red-rose-birthday-cake-images-462.html'

		const { status, url } = await textMaker(effect_url, match)

		if (url) return await message.sendFromUrl(url)

	}

)