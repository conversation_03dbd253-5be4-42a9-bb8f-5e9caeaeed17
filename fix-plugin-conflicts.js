#!/usr/bin/env node

/**
 * 🔧 Fix Plugin Conflicts That Cause Logout Issues
 * 
 * This script identifies and fixes plugin conflicts that cause
 * the "Intentional Logout" error when commands are executed.
 */

const fs = require('fs')
const path = require('path')

console.log('🔧 Fixing Plugin Conflicts')
console.log('==========================\n')

// List of conflicting plugins that cause logout issues
const conflictingPlugins = [
  'y2mate.js',     // Conflicts with our clean ytdl
  'yts.js',        // Might conflict with search
  'wcg.js'         // Word chain game - might have issues
]

// Backup directory
const backupDir = path.join(__dirname, 'plugins-backup')
if (!fs.existsSync(backupDir)) {
  fs.mkdirSync(backupDir)
  console.log('📁 Created backup directory: plugins-backup')
}

// Function to disable a plugin by renaming it
function disablePlugin(pluginName) {
  const pluginPath = path.join(__dirname, 'plugins', pluginName)
  const backupPath = path.join(backupDir, pluginName)
  const disabledPath = path.join(__dirname, 'plugins', `${pluginName}.disabled`)
  
  if (fs.existsSync(pluginPath)) {
    try {
      // Create backup
      fs.copyFileSync(pluginPath, backupPath)
      console.log(`📋 Backed up: ${pluginName}`)
      
      // Disable by renaming
      fs.renameSync(pluginPath, disabledPath)
      console.log(`🚫 Disabled: ${pluginName} → ${pluginName}.disabled`)
      
      return true
    } catch (error) {
      console.log(`❌ Failed to disable ${pluginName}: ${error.message}`)
      return false
    }
  } else {
    console.log(`⚠️ Plugin not found: ${pluginName}`)
    return false
  }
}

// Function to check for plugin conflicts
function checkPluginConflicts() {
  console.log('🔍 Checking for plugin conflicts...\n')
  
  const pluginsDir = path.join(__dirname, 'plugins')
  const plugins = fs.readdirSync(pluginsDir).filter(file => file.endsWith('.js'))
  
  // Check for YouTube-related plugins
  const youtubePlugins = plugins.filter(plugin => 
    plugin.includes('yt') || 
    plugin.includes('y2mate') || 
    plugin.includes('youtube')
  )
  
  console.log('📺 YouTube-related plugins found:')
  youtubePlugins.forEach(plugin => {
    console.log(`   • ${plugin}`)
  })
  
  // Check for game plugins that might cause issues
  const gamePlugins = plugins.filter(plugin => 
    plugin.includes('wcg') || 
    plugin.includes('game') || 
    plugin.includes('chain')
  )
  
  console.log('\n🎮 Game plugins found:')
  gamePlugins.forEach(plugin => {
    console.log(`   • ${plugin}`)
  })
  
  return { youtubePlugins, gamePlugins }
}

// Main fix function
async function fixConflicts() {
  const { youtubePlugins, gamePlugins } = checkPluginConflicts()
  
  console.log('\n🔧 Fixing conflicts...\n')
  
  let fixedCount = 0
  
  // Disable conflicting plugins
  conflictingPlugins.forEach(plugin => {
    if (disablePlugin(plugin)) {
      fixedCount++
    }
  })
  
  console.log(`\n✅ Fixed ${fixedCount} plugin conflicts`)
  
  // Check what's left active
  console.log('\n📋 Active YouTube plugins after fix:')
  const remainingPlugins = fs.readdirSync(path.join(__dirname, 'plugins'))
    .filter(file => file.endsWith('.js') && (file.includes('yt') || file.includes('y2mate')))
  
  if (remainingPlugins.length === 0) {
    console.log('   ❌ No YouTube plugins active!')
    console.log('   💡 Make sure ytdl-clean.js is working')
  } else {
    remainingPlugins.forEach(plugin => {
      console.log(`   ✅ ${plugin}`)
    })
  }
  
  console.log('\n📝 What was done:')
  console.log('   ✅ Backed up conflicting plugins to plugins-backup/')
  console.log('   ✅ Disabled conflicting plugins (renamed to .disabled)')
  console.log('   ✅ Left ytdl-clean.js as the only YouTube downloader')
  
  console.log('\n🚀 Next Steps:')
  console.log('   1. Restart your bot: node start-dual-sessions-fixed.js')
  console.log('   2. Wait for both sessions to connect')
  console.log('   3. Test: .ytdl never gonna give you up auto')
  console.log('   4. If it works, the logout issue should be fixed!')
  
  console.log('\n💡 If you need the disabled plugins back:')
  console.log('   • Check plugins-backup/ folder')
  console.log('   • Rename .disabled files back to .js (one at a time)')
  console.log('   • Test each one individually')
  
  console.log('\n✅ Plugin conflict fix complete!')
}

// Run the fix
fixConflicts().catch(error => {
  console.error('💥 Fix failed:', error.message)
  process.exit(1)
})
