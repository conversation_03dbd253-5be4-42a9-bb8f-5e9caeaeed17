/**
 * 🎬 Enhanced YouTube Downloader Plugin
 * 
 * A comprehensive YouTube download plugin with multiple features:
 * - Video downloads with quality selection
 * - Audio downloads in MP3 format
 * - Search functionality with interactive selection
 * - Auto-download with "auto" keyword
 * - Multiple fallback methods for reliability
 * - Uses your YouTube cookie for better access
 * 
 * Commands:
 * - .ytdl <url/search> - Download video with quality options
 * - .ytdl <search> auto - Auto download best quality video
 * - .ytdl audio <url/search> - Download audio only
 * - .ytdl audio <search> auto - Auto download audio
 * 
 * Examples:
 * .ytdl https://youtu.be/dQw4w9WgXcQ
 * .ytdl jingle bells auto
 * .ytdl audio never gonna give you up
 * .ytdl audio despacito auto
 */

const { bot, yts, y2mate, getBuffer, isUrl, generateList, addAudioMetaData } = require('../lib/')

// YouTube URL regex pattern
const ytIdRegex = /(?:http(?:s|):\/\/|)(?:(?:www\.|)youtube(?:\-nocookie|)\.com\/(?:watch\?.*(?:|\&)v=|embed|shorts\/|v\/)|youtu\.be\/)([-_0-9A-Za-z]{11})/

// Get YouTube cookie from config
const getYouTubeCookie = () => {
  return process.env.YT_COOKIE || process.env.YOUTUBE_COOKIE || ''
}

// Safe text formatting
const safeFormat = (text) => {
  if (!text) return 'Unknown'
  return text.replace(/[*_`~]/g, '').substring(0, 100)
}

// Main YouTube Downloader Command
bot(
  {
    pattern: 'ytdl ?(.*)',
    desc: 'Enhanced YouTube video/audio downloader with search',
    type: 'download',
  },
  async (message, match) => {
    match = match || message.reply_message?.text
    if (!match) {
      return await message.send(`*🎬 Enhanced YouTube Downloader*

*📥 Video Downloads:*
• \`.ytdl <YouTube URL>\` - Download with quality options
• \`.ytdl <search term>\` - Search and select video
• \`.ytdl <search term> auto\` - Auto download best quality

*🎵 Audio Downloads:*
• \`.ytdl audio <YouTube URL>\` - Download audio only
• \`.ytdl audio <search term>\` - Search and select audio
• \`.ytdl audio <search term> auto\` - Auto download audio

*📝 Examples:*
\`.ytdl https://youtu.be/dQw4w9WgXcQ\`
\`.ytdl jingle bells auto\`
\`.ytdl audio never gonna give you up\`
\`.ytdl audio despacito auto\``)
    }

    // Check if it's an audio download request
    const isAudioRequest = match.toLowerCase().startsWith('audio ')
    if (isAudioRequest) {
      match = match.substring(6).trim() // Remove "audio " prefix
      return await handleAudioDownload(message, match)
    }

    // Handle video download
    return await handleVideoDownload(message, match)
  }
)

// Handle video downloads
async function handleVideoDownload(message, match) {
  const vid = ytIdRegex.exec(match)
  const isAutoDownload = match.toLowerCase().includes(' auto')
  const searchTerm = match.replace(/ auto$/i, '').trim()

  // If not a direct URL, search first
  if (!vid) {
    try {
      await message.send('🔍 *Searching YouTube...*')
      const result = await yts(searchTerm, false, null, message.id)
      
      if (!result.length) {
        return await message.send(`❌ *No results found for:* ${searchTerm}`)
      }

      if (isAutoDownload) {
        // Auto download first result
        const topResult = result[0]
        await message.send(`🎬 *Auto-downloading:* ${safeFormat(topResult.title)}`)
        return await downloadVideo(message, topResult.id, topResult)
      } else {
        // Show search results for selection
        const msg = generateList(
          result.slice(0, 10).map(({ title, id, duration, view, author }) => ({
            text: `🎬 ${safeFormat(title)}\n⏱️ ${duration} | 👁️ ${view}\n👤 ${safeFormat(author)}\n`,
            id: `ytdl https://www.youtube.com/watch?v=${id}`,
          })),
          `🔍 *Search Results for:* ${searchTerm}\n\nSelect video to download:`,
          message.jid,
          message.participant,
          message.id
        )
        return await message.send(msg.message, { quoted: message.data }, msg.type)
      }
    } catch (error) {
      return await message.send(`❌ *Search failed:* ${error.message}`)
    }
  }

  // Direct URL download
  const videoId = vid[1]
  
  try {
    await message.send('📋 *Getting video info...*')
    const [videoInfo] = await yts(videoId, true, null, message.id)
    return await downloadVideo(message, videoId, videoInfo)
  } catch (error) {
    return await message.send(`❌ *Failed to get video info:* ${error.message}`)
  }
}

// Handle audio downloads
async function handleAudioDownload(message, match) {
  const vid = ytIdRegex.exec(match)
  const isAutoDownload = match.toLowerCase().includes(' auto')
  const searchTerm = match.replace(/ auto$/i, '').trim()

  // If not a direct URL, search first
  if (!vid) {
    try {
      await message.send('🔍 *Searching YouTube for audio...*')
      const result = await yts(searchTerm, false, null, message.id)
      
      if (!result.length) {
        return await message.send(`❌ *No results found for:* ${searchTerm}`)
      }

      if (isAutoDownload) {
        // Auto download first result
        const topResult = result[0]
        await message.send(`🎵 *Auto-downloading audio:* ${safeFormat(topResult.title)}`)
        return await downloadAudio(message, topResult.id, topResult)
      } else {
        // Show search results for selection
        const msg = generateList(
          result.slice(0, 10).map(({ title, id, duration, view, author }) => ({
            text: `🎵 ${safeFormat(title)}\n⏱️ ${duration} | 👁️ ${view}\n👤 ${safeFormat(author)}\n`,
            id: `ytdl audio https://www.youtube.com/watch?v=${id}`,
          })),
          `🔍 *Audio Search Results for:* ${searchTerm}\n\nSelect audio to download:`,
          message.jid,
          message.participant,
          message.id
        )
        return await message.send(msg.message, { quoted: message.data }, msg.type)
      }
    } catch (error) {
      return await message.send(`❌ *Audio search failed:* ${error.message}`)
    }
  }

  // Direct URL audio download
  const videoId = vid[1]
  
  try {
    await message.send('📋 *Getting audio info...*')
    const [videoInfo] = await yts(videoId, true, null, message.id)
    return await downloadAudio(message, videoId, videoInfo)
  } catch (error) {
    return await message.send(`❌ *Failed to get audio info:* ${error.message}`)
  }
}

// Download video with quality options
async function downloadVideo(message, videoId, videoInfo) {
  const safeTitle = safeFormat(videoInfo.title)
  const safeAuthor = safeFormat(videoInfo.author)
  const safeDuration = videoInfo.duration || 'Unknown'

  await message.send(`🎬 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n\n🔄 *Starting download...*`)

  // Quality preference order (best to lowest)
  const qualities = ['720p', '480p', '360p', '240p', '144p']
  
  for (const quality of qualities) {
    try {
      await message.send(`🔄 *Trying ${quality} quality...*`)
      
      // Try y2mate.get first
      const result = await y2mate.get(videoId, 'video', quality)
      
      if (isUrl(result)) {
        await message.send(`✅ *Success! Sending ${quality} video...*`)
        return await message.sendFromUrl(result, {
          quoted: message.data,
          caption: `🎬 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n📺 Quality: ${quality}`
        })
      }
      
      // Try y2mate.dl as fallback
      const downloadResult = await y2mate.dl(videoId, 'video', quality)
      if (downloadResult && isUrl(downloadResult)) {
        await message.send(`✅ *Success! Sending ${quality} video...*`)
        return await message.sendFromUrl(downloadResult, {
          quoted: message.data,
          caption: `🎬 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n📺 Quality: ${quality}`
        })
      }
      
    } catch (qualityError) {
      await message.send(`❌ *${quality} failed, trying next quality...*`)
      continue
    }
  }
  
  return await message.send(`❌ *Download failed for all qualities*\n\n💡 *Try:*\n• Check if the video is available\n• Try a different video\n• Use audio download: \`.ytdl audio ${videoId}\``)
}

// Download audio
async function downloadAudio(message, videoId, videoInfo) {
  const safeTitle = safeFormat(videoInfo.title)
  const safeAuthor = safeFormat(videoInfo.author)
  const safeDuration = videoInfo.duration || 'Unknown'

  await message.send(`🎵 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n\n🔄 *Downloading audio...*`)

  try {
    // Try y2mate.get for audio
    const result = await y2mate.get(videoId, 'audio')
    
    if (isUrl(result)) {
      await message.send('✅ *Success! Sending audio...*')
      return await message.sendFromUrl(result, {
        quoted: message.data,
        mimetype: 'audio/mpeg',
        fileName: `${safeTitle}.mp3`
      })
    }
    
    // Try y2mate.dl as fallback
    const downloadResult = await y2mate.dl(videoId, 'audio')
    if (downloadResult && isUrl(downloadResult)) {
      await message.send('✅ *Success! Sending audio...*')
      return await message.sendFromUrl(downloadResult, {
        quoted: message.data,
        mimetype: 'audio/mpeg',
        fileName: `${safeTitle}.mp3`
      })
    }
    
    throw new Error('No download URL returned')
    
  } catch (error) {
    return await message.send(`❌ *Audio download failed:* ${error.message}\n\n💡 *Try:*\n• Check if the video is available\n• Try a different video\n• Use video download: \`.ytdl ${videoId}\``)
  }
}
