/**
 * YouTube Download Test Script
 * 
 * This script helps test different YouTube download methods to identify which ones work.
 * Run this to troubleshoot YouTube download issues.
 */

const axios = require('axios')

// Test video ID (<PERSON> Roll - should always be available)
const testVideoId = 'dQw4w9WgXcQ'
const testVideoUrl = `https://www.youtube.com/watch?v=${testVideoId}`

console.log('🧪 Testing YouTube Download Methods...\n')

async function testMethod1() {
  console.log('🔄 Testing Method 1: Working Cobalt API...')
  try {
    const response = await axios.post('https://co.wuk.sh/api/json', {
      url: testVideoUrl,
      vQuality: '720',
      vFormat: 'mp4',
      isAudioOnly: false
    }, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 30000
    })

    if (response.data && response.data.url) {
      console.log('✅ Method 1 SUCCESS:', response.data.url.substring(0, 50) + '...')
      return true
    } else {
      console.log('❌ Method 1 FAILED: No download URL returned')
      console.log('Response:', JSON.stringify(response.data, null, 2))
      return false
    }
  } catch (error) {
    console.log('❌ Method 1 ERROR:', error.message)
    return false
  }
}

async function testMethod2() {
  console.log('🔄 Testing Method 2: Y2Mate Library...')
  try {
    // Test the existing y2mate library
    const { y2mate } = require('./lib/')
    const result = await y2mate.get(testVideoId, 'video')

    if (result && typeof result === 'string' && result.includes('http')) {
      console.log('✅ Method 2 SUCCESS:', result.substring(0, 50) + '...')
      return true
    } else {
      console.log('❌ Method 2 FAILED: No download URL returned')
      console.log('Response:', result)
      return false
    }
  } catch (error) {
    console.log('❌ Method 2 ERROR:', error.message)
    return false
  }
}

async function testMethod3() {
  console.log('🔄 Testing Method 3: Direct YouTube Access...')
  try {
    const response = await axios.get(`https://www.youtube.com/watch?v=${testVideoId}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 30000
    })

    if (response.data && response.data.includes('videoDetails')) {
      console.log('✅ Method 3 SUCCESS: Can access YouTube page')
      return true
    } else {
      console.log('❌ Method 3 FAILED: Cannot access YouTube page')
      return false
    }
  } catch (error) {
    console.log('❌ Method 3 ERROR:', error.message)
    return false
  }
}

async function testMethod4() {
  console.log('🔄 Testing Method 4: Network Connectivity...')
  try {
    // Test basic internet connectivity
    const response = await axios.get('https://www.google.com', {
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })

    if (response.status === 200) {
      console.log('✅ Method 4 SUCCESS: Internet connection working')
      return true
    } else {
      console.log('❌ Method 4 FAILED: Internet connection issue')
      return false
    }
  } catch (error) {
    console.log('❌ Method 4 ERROR:', error.message)
    return false
  }
}

async function runTests() {
  console.log(`📺 Testing with video: ${testVideoUrl}\n`)
  
  const results = []
  
  results.push(await testMethod1())
  console.log('')
  
  results.push(await testMethod2())
  console.log('')
  
  results.push(await testMethod3())
  console.log('')
  
  results.push(await testMethod4())
  console.log('')
  
  const successCount = results.filter(r => r).length
  
  console.log('📊 TEST RESULTS:')
  console.log(`✅ Working methods: ${successCount}/4`)
  console.log(`❌ Failed methods: ${4 - successCount}/4`)
  
  if (successCount === 0) {
    console.log('\n🚨 ALL METHODS FAILED!')
    console.log('This suggests a network issue or all APIs are down.')
    console.log('Try again later or check your internet connection.')
  } else {
    console.log(`\n🎉 ${successCount} method(s) are working!`)
    console.log('The ytdl plugin should work with at least one of these methods.')
  }
}

// Run the tests
runTests().catch(console.error)
