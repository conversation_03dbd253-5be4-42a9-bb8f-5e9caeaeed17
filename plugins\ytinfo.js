const { bot, yts } = require('../lib/')

const ytIdRegex = /(?:http(?:s|):\/\/|)(?:(?:www\.|)youtube(?:\-nocookie|)\.com\/(?:watch\?.*(?:|\&)v=|embed|shorts\/|v\/)|youtu\.be\/)([-_0-9A-Za-z]{11})/

// YouTube Video Information
bot(
  {
    pattern: 'ytinfo ?(.*)',
    desc: 'Get detailed YouTube video information',
    type: 'search',
  },
  async (message, match) => {
    match = match || message.reply_message.text
    if (!match) return await message.send('*📺 YouTube Video Info*\n\n*Usage:*\n• `.ytinfo <YouTube URL>`\n• `.ytinfo <search term>`\n\n*Example:*\n`.ytinfo https://youtu.be/dQw4w9WgXcQ`')

    const vid = ytIdRegex.exec(match)
    
    try {
      if (vid) {
        // Direct URL provided
        const [result] = await yts(vid[1], true, null, message.id)
        const { 
          title, 
          description, 
          duration, 
          view, 
          published, 
          author, 
          thumbnail,
          id,
          url
        } = result

        const info = `📺 *YouTube Video Information*\n\n` +
          `🎬 *Title:* ${title}\n` +
          `👤 *Channel:* ${author}\n` +
          `⏱️ *Duration:* ${duration}\n` +
          `👁️ *Views:* ${view}\n` +
          `📅 *Published:* ${published}\n` +
          `🆔 *Video ID:* ${id}\n` +
          `🔗 *URL:* ${url}\n\n` +
          `📝 *Description:*\n${description.length > 200 ? description.substring(0, 200) + '...' : description}\n\n` +
          `💡 *Download Options:*\n` +
          `• \`.ytdl ${url}\` - Download video\n` +
          `• \`.ytmp3 ${url}\` - Download audio\n` +
          `• \`.ytv ${url}\` - Y2mate video\n` +
          `• \`.yta ${url}\` - Y2mate audio`

        if (thumbnail && thumbnail.url) {
          return await message.sendFromUrl(thumbnail.url, {
            caption: info,
            quoted: message.data
          })
        } else {
          return await message.send(info, { quoted: message.data })
        }
      } else {
        // Search term provided
        const results = await yts(match, false, null, message.id)
        if (!results.length) return await message.send(`❌ *No results found for:* ${match}`)

        const topResult = results[0]
        const { 
          title, 
          duration, 
          view, 
          published, 
          author, 
          thumbnail,
          id,
          url
        } = topResult

        const info = `🔍 *Search Result for:* ${match}\n\n` +
          `📺 *Top Result:*\n\n` +
          `🎬 *Title:* ${title}\n` +
          `👤 *Channel:* ${author}\n` +
          `⏱️ *Duration:* ${duration}\n` +
          `👁️ *Views:* ${view}\n` +
          `📅 *Published:* ${published}\n` +
          `🆔 *Video ID:* ${id}\n` +
          `🔗 *URL:* ${url}\n\n` +
          `💡 *Download Options:*\n` +
          `• \`.ytdl ${url}\` - Download video\n` +
          `• \`.ytmp3 ${url}\` - Download audio\n\n` +
          `📋 *More Results:*\n` +
          `• \`.yts ${match}\` - See all results`

        if (thumbnail && thumbnail.url) {
          return await message.sendFromUrl(thumbnail.url, {
            caption: info,
            quoted: message.data
          })
        } else {
          return await message.send(info, { quoted: message.data })
        }
      }
    } catch (error) {
      return await message.send(`❌ *Error getting video info:* ${error.message}`, { quoted: message.data })
    }
  }
)

// YouTube Channel Information
bot(
  {
    pattern: 'ytchannel ?(.*)',
    desc: 'Get YouTube channel information',
    type: 'search',
  },
  async (message, match) => {
    match = match || message.reply_message.text
    if (!match) return await message.send('*📺 YouTube Channel Info*\n\n*Usage:*\n• `.ytchannel <channel name>`\n• `.ytchannel <channel URL>`\n\n*Example:*\n`.ytchannel MrBeast`')

    try {
      // Search for videos from the channel to get channel info
      const results = await yts(match, false, null, message.id)
      if (!results.length) return await message.send(`❌ *No results found for:* ${match}`)

      // Group results by channel
      const channelMap = new Map()
      results.forEach(video => {
        if (!channelMap.has(video.author)) {
          channelMap.set(video.author, [])
        }
        channelMap.get(video.author).push(video)
      })

      // Get the channel with most videos in results (likely the target)
      let targetChannel = null
      let maxVideos = 0
      
      for (const [channel, videos] of channelMap) {
        if (videos.length > maxVideos || channel.toLowerCase().includes(match.toLowerCase())) {
          maxVideos = videos.length
          targetChannel = { name: channel, videos }
        }
      }

      if (!targetChannel) {
        return await message.send(`❌ *No channel found for:* ${match}`)
      }

      const { name, videos } = targetChannel
      const recentVideos = videos.slice(0, 5)

      let info = `📺 *Channel Information*\n\n` +
        `👤 *Channel:* ${name}\n` +
        `📊 *Videos Found:* ${videos.length} (in search results)\n\n` +
        `🎬 *Recent Videos:*\n`

      recentVideos.forEach((video, index) => {
        info += `\n${index + 1}. *${video.title}*\n` +
          `   ⏱️ ${video.duration} | 👁️ ${video.view}\n` +
          `   📅 ${video.published}\n` +
          `   🔗 ${video.url}\n`
      })

      info += `\n💡 *Commands:*\n` +
        `• \`.yts ${name}\` - Search more videos\n` +
        `• \`.ytdl <video_url>\` - Download video\n` +
        `• \`.ytmp3 <video_url>\` - Download audio`

      return await message.send(info, { quoted: message.data })

    } catch (error) {
      return await message.send(`❌ *Error getting channel info:* ${error.message}`, { quoted: message.data })
    }
  }
)

// YouTube Trending (Popular videos)
bot(
  {
    pattern: 'yttrending',
    desc: 'Get trending YouTube videos',
    type: 'search',
  },
  async (message) => {
    try {
      // Search for popular/trending terms
      const trendingTerms = ['trending', 'viral', 'popular', 'music', 'news']
      const randomTerm = trendingTerms[Math.floor(Math.random() * trendingTerms.length)]
      
      const results = await yts(randomTerm, false, null, message.id)
      if (!results.length) return await message.send('❌ *No trending videos found*')

      const topVideos = results.slice(0, 10)
      
      let info = `🔥 *Trending YouTube Videos*\n\n`
      
      topVideos.forEach((video, index) => {
        info += `${index + 1}. *${video.title}*\n` +
          `   👤 ${video.author}\n` +
          `   ⏱️ ${video.duration} | 👁️ ${video.view}\n` +
          `   📅 ${video.published}\n` +
          `   🔗 ${video.url}\n\n`
      })

      info += `💡 *To download:*\n` +
        `• \`.ytdl <video_url>\` - Download video\n` +
        `• \`.ytmp3 <video_url>\` - Download audio`

      return await message.send(info, { quoted: message.data })

    } catch (error) {
      return await message.send(`❌ *Error getting trending videos:* ${error.message}`, { quoted: message.data })
    }
  }
)
