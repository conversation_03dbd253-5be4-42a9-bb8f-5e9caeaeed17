#!/bin/bash

echo "🤖 Levanter Multi-Session Starter"
echo "================================="

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
    echo "❌ PM2 is not installed. Installing PM2..."
    npm install -g pm2
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Check if config.json exists
if [ ! -f "config.json" ]; then
    echo "❌ config.json not found. Please create it with your session configurations."
    exit 1
fi

# Stop any existing sessions
echo "🛑 Stopping existing sessions..."
pm2 delete ecosystem.config.js 2>/dev/null || true

# Start all sessions using PM2
echo "🚀 Starting all sessions..."
pm2 start ecosystem.config.js

# Show status
echo ""
echo "📊 Session Status:"
pm2 status

echo ""
echo "📝 Useful Commands:"
echo "   pm2 status                    - Show all sessions status"
echo "   pm2 logs                      - Show all logs"
echo "   pm2 logs levanter-session1    - Show specific session logs"
echo "   pm2 restart all               - Restart all sessions"
echo "   pm2 stop all                  - Stop all sessions"
echo "   pm2 delete all                - Delete all sessions"
echo ""
echo "✅ Multi-session setup complete!"
