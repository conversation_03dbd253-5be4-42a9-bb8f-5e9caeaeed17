/**
 * 🎵 Simple YT1Z.net YouTube Downloader Plugin
 * 
 * A simplified version that works more reliably with yt1z.net/en/
 * Commands: .yt1z <url/search> - Download using yt1z.net/en/
 */

const { bot, yts, isUrl } = require('../lib/')
const axios = require('axios')

// YouTube URL regex
const ytIdRegex = /(?:http(?:s|):\/\/|)(?:(?:www\.|)youtube(?:\-nocookie|)\.com\/(?:watch\?.*(?:|\&)v=|embed|shorts\/|v\/)|youtu\.be\/)([-_0-9A-Za-z]{11})/

// Safe text formatting
const safeFormat = (text) => {
  if (!text) return 'Unknown'
  return String(text).replace(/[*_`~]/g, '').substring(0, 100)
}

// Simple YT1Z downloader that mimics browser behavior
async function downloadFromYT1Z(videoUrl) {
  try {
    console.log('YT1Z: Starting download process...')
    
    // Step 1: Get the main page to establish session
    const mainPageResponse = await axios.get('https://yt1z.net/en/', {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      },
      timeout: 10000
    })
    
    if (mainPageResponse.status !== 200) {
      throw new Error('Cannot access yt1z.net/en/')
    }
    
    console.log('YT1Z: Main page loaded successfully')
    
    // Step 2: Submit the YouTube URL
    const formData = new URLSearchParams()
    formData.append('query', videoUrl)
    
    const submitResponse = await axios.post('https://yt1z.net/en/', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Referer': 'https://yt1z.net/en/',
        'Origin': 'https://yt1z.net',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      },
      maxRedirects: 5,
      timeout: 15000
    })
    
    console.log('YT1Z: URL submitted, processing response...')
    
    const html = submitResponse.data
    
    // Step 3: Look for download links in various formats
    const downloadPatterns = [
      // Direct download links
      /href="([^"]*\.mp3[^"]*)"[^>]*(?:download|Download)/i,
      /href="([^"]*download[^"]*\.mp3[^"]*)"[^>]*>/i,
      // Data attributes
      /data-url="([^"]*\.mp3[^"]*)"/i,
      /data-download="([^"]*\.mp3[^"]*)"/i,
      // JavaScript variables
      /downloadUrl\s*[:=]\s*["']([^"']*\.mp3[^"']*)["']/i,
      /mp3Url\s*[:=]\s*["']([^"']*\.mp3[^"']*)["']/i,
      // JSON embedded
      /"downloadUrl":"([^"]*\.mp3[^"]*)"/,
      /"mp3":"([^"]*)"/,
      /"url":"([^"]*\.mp3[^"]*)"/,
      // Form actions that might lead to downloads
      /action="([^"]*convert[^"]*)"[^>]*>/i
    ]
    
    for (const pattern of downloadPatterns) {
      const match = html.match(pattern)
      if (match && match[1]) {
        let downloadUrl = match[1]
        
        // Skip if it's just a form action
        if (downloadUrl.includes('convert') && !downloadUrl.includes('.mp3')) {
          continue
        }
        
        // Ensure it's a full URL
        if (!downloadUrl.startsWith('http')) {
          if (downloadUrl.startsWith('/')) {
            downloadUrl = `https://yt1z.net${downloadUrl}`
          } else {
            downloadUrl = `https://yt1z.net/en/${downloadUrl}`
          }
        }
        
        // Validate that it looks like a real download URL
        if (downloadUrl.includes('.mp3') || downloadUrl.includes('download')) {
          console.log('YT1Z: Found download URL:', downloadUrl.substring(0, 80) + '...')
          return downloadUrl
        }
      }
    }
    
    // Step 4: If no direct link found, try to find conversion endpoint
    const conversionMatch = html.match(/(?:action|href)="([^"]*(?:convert|download)[^"]*)"/)
    if (conversionMatch) {
      console.log('YT1Z: Found conversion endpoint, trying...')
      return await processConversion(conversionMatch[1], videoUrl)
    }
    
    throw new Error('No download links found in yt1z.net response')
    
  } catch (error) {
    console.error('YT1Z Download Error:', error.message)
    throw new Error(`YT1Z failed: ${error.message}`)
  }
}

// Process conversion if needed
async function processConversion(endpoint, videoUrl) {
  try {
    const convertUrl = endpoint.startsWith('http') ? endpoint : `https://yt1z.net${endpoint}`
    
    const formData = new URLSearchParams()
    formData.append('url', videoUrl)
    formData.append('format', 'mp3')
    
    const response = await axios.post(convertUrl, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://yt1z.net/en/',
        'Origin': 'https://yt1z.net'
      },
      timeout: 20000
    })
    
    const html = response.data
    const linkMatch = html.match(/href="([^"]*\.mp3[^"]*)"[^>]*(?:download|Download)/i)
    
    if (linkMatch) {
      let downloadUrl = linkMatch[1]
      if (!downloadUrl.startsWith('http')) {
        downloadUrl = `https://yt1z.net${downloadUrl}`
      }
      return downloadUrl
    }
    
    throw new Error('Conversion failed to produce download link')
    
  } catch (error) {
    throw new Error(`Conversion error: ${error.message}`)
  }
}

// Main plugin command
bot(
  {
    pattern: 'yt1z ?(.*)',
    fromMe: false,
    desc: 'Download YouTube audio using yt1z.net/en/ service',
    type: 'download',
  },
  async (message, match) => {
    try {
      match = match || message.reply_message?.text
      if (!match) {
        return await message.send(`*🎵 YT1Z.net YouTube Downloader*

*📝 Usage:*
• \`.yt1z <YouTube URL>\` - Download audio from URL
• \`.yt1z <search term>\` - Search and download audio
• \`.yt1z <search term> auto\` - Auto download first result

*📝 Examples:*
\`.yt1z https://youtu.be/dQw4w9WgXcQ\`
\`.yt1z never gonna give you up auto\`
\`.yt1z despacito\`

*🌐 Powered by yt1z.net/en/*`)
      }

      const vid = ytIdRegex.exec(match)
      const isAutoDownload = match.toLowerCase().includes(' auto')
      const searchTerm = match.replace(/ auto$/i, '').trim()

      let videoUrl, videoInfo

      // If not a direct URL, search first
      if (!vid) {
        await message.send('🔍 *Searching YouTube...*')
        const result = await yts(searchTerm, false, null, message.id)
        
        if (!result || !result.length) {
          return await message.send(`❌ *No results found for:* ${searchTerm}`)
        }

        if (isAutoDownload) {
          // Auto download first result
          const topResult = result[0]
          videoUrl = `https://www.youtube.com/watch?v=${topResult.id}`
          videoInfo = topResult
          await message.send(`🎵 *Auto-downloading via YT1Z:* ${safeFormat(topResult.title)}`)
        } else {
          // Show search results for selection
          const { generateList } = require('../lib/')
          const msg = generateList(
            result.slice(0, 8).map(({ title, id, duration, view, author }) => ({
              text: `🎵 ${safeFormat(title)}\n⏱️ ${duration || 'Unknown'} | 👁️ ${view || 'Unknown'}\n👤 ${safeFormat(author)}\n`,
              id: `yt1z https://www.youtube.com/watch?v=${id}`,
            })),
            `🔍 *YT1Z Search Results for:* ${searchTerm}\n\nSelect audio to download:`,
            message.jid,
            message.participant,
            message.id
          )
          return await message.send(msg.message, { quoted: message.data }, msg.type)
        }
      } else {
        // Direct URL
        const videoId = vid[1]
        videoUrl = `https://www.youtube.com/watch?v=${videoId}`
        
        await message.send('📋 *Getting video info...*')
        const [info] = await yts(videoId, true, null, message.id)
        videoInfo = info
      }

      // Download using YT1Z
      const safeTitle = safeFormat(videoInfo.title)
      const safeAuthor = safeFormat(videoInfo.author)
      const safeDuration = videoInfo.duration || 'Unknown'

      await message.send(`🎵 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n\n🌐 *Processing via YT1Z.net/en/...*`)

      const downloadUrl = await downloadFromYT1Z(videoUrl)
      
      if (!isUrl(downloadUrl)) {
        throw new Error('Invalid download URL received from YT1Z')
      }

      await message.send('✅ *YT1Z processing complete! Sending audio...*')
      
      return await message.sendFromUrl(downloadUrl, {
        quoted: message.data,
        mimetype: 'audio/mpeg',
        fileName: `${safeTitle}.mp3`
      })
      
    } catch (error) {
      console.error('YT1Z Plugin Error:', error)
      return await message.send(`❌ *YT1Z download failed:* ${error.message}\n\n💡 *Try:*\n• Different video\n• Use regular \`.play\` command\n• Check if yt1z.net/en/ is accessible`)
    }
  }
)
