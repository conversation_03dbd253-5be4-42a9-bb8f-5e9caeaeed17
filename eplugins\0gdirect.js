const { bot } = require('../lib');
const axios = require('axios');

const MAX_FILE_SIZE = 2 * 1024 * 1024 * 1024; // 2GB limit

bot(
  {
    pattern: 'gdirect ?(.*)',
    fromMe: true,
    desc: 'Download files from Google Drive direct link',
    type: 'downloader',
  },
  async (message, match) => {
    const driveLink = match.trim();
    if (!driveLink) return await message.send('❌ *Usage:* `.gdrive <google drive direct link>`');

    try {
      await message.send('*📥 Downloading file...*');

      // 🔍 Extracting file ID
      let fileId;
      if (driveLink.includes('drive.google.com')) {
        const matchId = driveLink.match(/[-\w]{25,}/);
        fileId = matchId ? matchId[0] : null;
      } else {
        fileId = new URL(driveLink).searchParams.get('id');
      }

      if (!fileId) return await message.send('❌ *Invalid Google Drive link!*');

      const downloadUrl = `https://drive.usercontent.google.com/download?id=${fileId}&export=download&confirm=t`;

      // 🌍 Fetching file metadata
      const headResponse = await axios.head(downloadUrl, { validateStatus: false });

      if (!headResponse.headers['content-length']) {
        return await message.send('❌ *Could not retrieve file info. Check the link!*');
      }

      const contentLength = parseInt(headResponse.headers['content-length'], 10);
      if (contentLength > MAX_FILE_SIZE) {
        return await message.send(`❌ *File size exceeds 2GB limit! (${(contentLength / (1024 * 1024 * 1024)).toFixed(2)} GB)*`);
      }

      const fileName = headResponse.headers['content-disposition']
        ? decodeURIComponent(headResponse.headers['content-disposition'].split('filename=')[1]?.replace(/['"]/g, ''))
        : `file_${fileId}`;

      const fileSize = (contentLength / (1024 * 1024)).toFixed(2); // Convert to MB
      const fileExtension = fileName.split('.').pop(); // Get file extension

      // 📥 Downloading file
      const response = await axios({
        url: downloadUrl,
        method: 'GET',
        responseType: 'arraybuffer',
      });

      // 📝 Creating caption
      const caption = `*${fileName}*\n\n*${fileSize} MB*\n\n⏤͟͟͞͞★❮𝗦 𝗔 𝗩 𝗜❯⏤͟͟͞͞★`;

      // 📤 Sending file
      await message.send(Buffer.from(response.data), {
        mimetype: `video/${fileExtension}`,
        filename: fileName,
        caption: caption,
      }, 'document');

      await message.send('*✅ File sent successfully!*');
    } catch (error) {
      console.error(error);
      await message.send('🚨 *Error occurred! Check the link and try again.*');
    }
  }
);