#!/usr/bin/env node

/**
 * Fixed Multi-Session Manager
 * 
 * This script fixes the logout issue by properly isolating sessions
 * and implementing smart startup delays.
 */

const { spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

// Load session configurations
const configPath = path.join(__dirname, 'config.json')
let sessionConfigs = {}

if (fs.existsSync(configPath)) {
  try {
    sessionConfigs = JSON.parse(fs.readFileSync(configPath, 'utf8'))
  } catch (error) {
    console.error('Failed to load config.json:', error.message)
    process.exit(1)
  }
} else {
  console.error('config.json not found. Please create it with your session configurations.')
  process.exit(1)
}

const runningProcesses = []

// Enhanced session startup with better isolation
const startSession = (sessionName, sessionConfig, delay = 0) => {
  setTimeout(() => {
    console.log(`🚀 Starting session: ${sessionName} (delay: ${delay}ms)`)

    if (!sessionConfig.SESSION_ID || sessionConfig.SESSION_ID === 'levanter_sessionid') {
      console.warn(`⚠️  Skipping ${sessionName}: Invalid or placeholder SESSION_ID`)
      return null
    }

    // Create session-specific directories
    const sessionDirs = {
      auth: path.join(__dirname, 'auth', sessionName),
      temp: path.join(__dirname, 'temp', sessionName),
      logs: path.join(__dirname, 'logs', sessionName),
      cache: path.join(__dirname, 'cache', sessionName)
    }

    // Ensure all directories exist
    Object.values(sessionDirs).forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
        console.log(`📁 Created directory: ${dir}`)
      }
    })

    // Enhanced environment variables with better isolation
    const env = {
      ...process.env,
      ...sessionConfig,
      // Session-specific paths
      AUTH_PATH: sessionDirs.auth,
      TEMP_DIR: sessionDirs.temp,
      LOG_DIR: sessionDirs.logs,
      CACHE_DIR: sessionDirs.cache,
      // Unique database path for each session
      DATABASE_URL: path.join(__dirname, `database_${sessionName}.db`),
      // Session identification
      SESSION_NAME: sessionName,
      SESSION_INDEX: Object.keys(sessionConfigs).indexOf(sessionName),
      // Prevent conflicts
      NODE_ENV: 'production',
      // Browser isolation
      BROWSER_NAME: `Levanter-${sessionName}`,
      // Port isolation (if needed)
      PORT: 3000 + Object.keys(sessionConfigs).indexOf(sessionName),
      // Prevent aggressive reconnection
      RECONNECT_DELAY: '10000',
      MAX_RECONNECT_ATTEMPTS: '3'
    }

    // Start the session as a separate process with enhanced isolation
    const child = spawn('node', ['index.js'], {
      env,
      stdio: ['inherit', 'pipe', 'pipe'],
      cwd: __dirname,
      detached: false
    })

    // Enhanced logging
    child.stdout.on('data', (data) => {
      const logFile = path.join(sessionDirs.logs, 'output.log')
      fs.appendFileSync(logFile, `[${new Date().toISOString()}] ${data}`)
      console.log(`[${sessionName}] ${data.toString().trim()}`)
    })

    child.stderr.on('data', (data) => {
      const logFile = path.join(sessionDirs.logs, 'error.log')
      fs.appendFileSync(logFile, `[${new Date().toISOString()}] ${data}`)
      console.error(`[${sessionName}] ERROR: ${data.toString().trim()}`)
    })

    child.on('close', (code) => {
      console.log(`[${sessionName}] Process exited with code ${code}`)
      
      // Remove from running processes
      const index = runningProcesses.findIndex(p => p.name === sessionName)
      if (index > -1) {
        runningProcesses.splice(index, 1)
      }

      // Auto-restart with increased delay if crashed
      if (code !== 0 && code !== null) {
        const restartDelay = 30000 + (Math.random() * 10000) // 30-40 seconds
        console.log(`[${sessionName}] Restarting in ${restartDelay/1000}s...`)
        setTimeout(() => {
          startSession(sessionName, sessionConfig, 0)
        }, restartDelay)
      }
    })

    child.on('error', (error) => {
      console.error(`[${sessionName}] Failed to start:`, error.message)
    })

    // Store process info
    runningProcesses.push({
      name: sessionName,
      process: child,
      startTime: Date.now(),
      config: sessionConfig
    })

    return child
  }, delay)
}

// Graceful shutdown handler
const shutdown = () => {
  console.log('\n🛑 Shutting down all sessions...')
  
  runningProcesses.forEach(({ name, process }) => {
    if (process && !process.killed) {
      console.log(`🔴 Stopping ${name}...`)
      process.kill('SIGTERM')
    }
  })

  setTimeout(() => {
    console.log('✅ All sessions stopped')
    process.exit(0)
  }, 5000)
}

// Handle shutdown signals
process.on('SIGINT', shutdown)
process.on('SIGTERM', shutdown)

// Start all sessions with smart delays
const start = () => {
  console.log('🤖 Fixed Levanter Multi-Session Manager')
  console.log('======================================')

  const sessionNames = Object.keys(sessionConfigs).filter(name => 
    sessionConfigs[name].SESSION_ID && 
    sessionConfigs[name].SESSION_ID !== 'levanter_sessionid'
  )

  if (sessionNames.length === 0) {
    console.error('❌ No valid sessions configured in config.json')
    process.exit(1)
  }

  console.log(`📋 Found ${sessionNames.length} valid session(s): ${sessionNames.join(', ')}`)
  console.log('⏹️  Press Ctrl+C to stop all sessions\n')

  // Start sessions with progressive delays to prevent conflicts
  sessionNames.forEach((sessionName, index) => {
    const delay = index * 45000 // 45 seconds between each session
    const sessionConfig = sessionConfigs[sessionName]
    
    console.log(`⏰ ${sessionName} scheduled to start in ${delay/1000}s`)
    startSession(sessionName, sessionConfig, delay)
  })

  // Status monitoring
  setInterval(() => {
    console.log(`\n📊 Status: ${runningProcesses.length}/${sessionNames.length} sessions running`)
    runningProcesses.forEach(({ name, startTime }) => {
      const uptime = Math.round((Date.now() - startTime) / 1000)
      console.log(`   ✅ ${name}: ${uptime}s uptime`)
    })
  }, 60000) // Every minute
}

// Start the manager
start()

// Keep the main process alive
process.stdin.resume()
