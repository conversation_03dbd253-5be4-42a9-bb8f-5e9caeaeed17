#!/usr/bin/env node

/**
 * Session Health Monitor
 * 
 * This script monitors your WhatsApp sessions and detects logout issues
 */

const fs = require('fs')
const path = require('path')

// Monitor session health
function monitorSessions() {
  console.log('🔍 Session Health Monitor Started')
  console.log('=================================\n')

  const configPath = path.join(__dirname, 'config.json')
  
  if (!fs.existsSync(configPath)) {
    console.error('❌ config.json not found')
    return
  }

  const sessionConfigs = JSON.parse(fs.readFileSync(configPath, 'utf8'))
  const sessionNames = Object.keys(sessionConfigs).filter(name => 
    sessionConfigs[name].SESSION_ID && 
    sessionConfigs[name].SESSION_ID !== 'levanter_sessionid'
  )

  console.log(`📱 Monitoring ${sessionNames.length} sessions: ${sessionNames.join(', ')}\n`)

  sessionNames.forEach(sessionName => {
    const authDir = path.join(__dirname, 'auth', sessionName)
    const logDir = path.join(__dirname, 'logs', sessionName)
    const tempDir = path.join(__dirname, 'temp', sessionName)

    console.log(`🔍 Checking ${sessionName}:`)
    
    // Check auth directory
    if (fs.existsSync(authDir)) {
      const authFiles = fs.readdirSync(authDir)
      console.log(`   ✅ Auth directory: ${authFiles.length} files`)
      
      // Check for session files
      const hasSession = authFiles.some(file => file.includes('session') || file.includes('creds'))
      console.log(`   ${hasSession ? '✅' : '❌'} Session files: ${hasSession ? 'Present' : 'Missing'}`)
    } else {
      console.log(`   ❌ Auth directory: Missing`)
    }

    // Check log files
    if (fs.existsSync(logDir)) {
      const logFiles = fs.readdirSync(logDir)
      console.log(`   ✅ Log directory: ${logFiles.length} files`)
      
      // Check recent activity
      const outputLog = path.join(logDir, 'output.log')
      if (fs.existsSync(outputLog)) {
        const stats = fs.statSync(outputLog)
        const lastModified = Date.now() - stats.mtime.getTime()
        const isRecent = lastModified < 300000 // 5 minutes
        console.log(`   ${isRecent ? '✅' : '⚠️'} Recent activity: ${Math.round(lastModified/1000)}s ago`)
      }
    } else {
      console.log(`   ⚠️ Log directory: Missing`)
    }

    // Check database
    const dbPath = path.join(__dirname, `database_${sessionName}.db`)
    if (fs.existsSync(dbPath)) {
      const stats = fs.statSync(dbPath)
      console.log(`   ✅ Database: ${Math.round(stats.size/1024)}KB`)
    } else {
      console.log(`   ❌ Database: Missing`)
    }

    console.log('')
  })

  // Check for common issues
  console.log('🔧 Common Issues Check:')
  
  // Check for port conflicts
  const usedPorts = sessionNames.map((_, index) => 3000 + index)
  console.log(`   📡 Ports in use: ${usedPorts.join(', ')}`)
  
  // Check for duplicate SESSION_IDs
  const sessionIds = sessionNames.map(name => sessionConfigs[name].SESSION_ID)
  const duplicates = sessionIds.filter((id, index) => sessionIds.indexOf(id) !== index)
  if (duplicates.length > 0) {
    console.log(`   ❌ Duplicate SESSION_IDs found: ${duplicates.join(', ')}`)
  } else {
    console.log(`   ✅ No duplicate SESSION_IDs`)
  }

  // Check config.env for conflicts
  const configEnvPath = path.join(__dirname, 'config.env')
  if (fs.existsSync(configEnvPath)) {
    const configEnv = fs.readFileSync(configEnvPath, 'utf8')
    if (configEnv.includes('SESSION_ID') && configEnv.includes(',')) {
      console.log(`   ❌ config.env has multiple SESSION_IDs (this causes conflicts!)`)
    } else {
      console.log(`   ✅ config.env looks good`)
    }
  }

  console.log('\n💡 Recommendations:')
  console.log('   • Start sessions with 45+ second delays')
  console.log('   • Use separate auth directories for each session')
  console.log('   • Monitor logs for "logout" or "disconnected" messages')
  console.log('   • Restart sessions one at a time if issues occur')
}

// Run monitor
monitorSessions()

// Option to run continuously
if (process.argv.includes('--watch')) {
  console.log('\n🔄 Running in watch mode (every 5 minutes)...')
  setInterval(monitorSessions, 300000) // Every 5 minutes
}
