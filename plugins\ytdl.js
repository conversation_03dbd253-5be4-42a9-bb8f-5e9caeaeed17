const { bot, yts, y2mate, getBuffer, isUrl, generateList } = require('../lib/')
const axios = require('axios')

const ytIdRegex = /(?:http(?:s|):\/\/|)(?:(?:www\.|)youtube(?:\-nocookie|)\.com\/(?:watch\?.*(?:|\&)v=|embed|shorts\/|v\/)|youtu\.be\/)([-_0-9A-Za-z]{11})/

// Enhanced YouTube Video Downloader with multiple fallback methods
bot(
  {
    pattern: 'ytdl ?(.*)',
    desc: 'Enhanced YouTube video downloader with multiple methods',
    type: 'download',
  },
  async (message, match) => {
    match = match || message.reply_message.text
    if (!match) return await message.send('*📥 Enhanced YouTube Downloader*\n\n*Usage:*\n• `.ytdl <YouTube URL>`\n• `.ytdl <search term>`\n\n*Example:*\n`.ytdl https://youtu.be/dQw4w9WgXcQ`')

    const vid = ytIdRegex.exec(match)
    
    // If not a direct URL, search first
    if (!vid) {
      try {
        const result = await yts(match, false, null, message.id)
        if (!result.length) return await message.send(`❌ *No results found for:* ${match}`)

        // Check if user wants auto-download (add "auto" or "first" to search)
        if (match.includes(' auto') || match.includes(' first') || match.includes(' download')) {
          const searchTerm = match.replace(/ (auto|first|download)/g, '').trim()
          const topResult = result[0]

          await message.send(
            `🎯 *Auto-downloading first result for:* ${searchTerm}\n\n` +
            `🎬 *${topResult.title}*\n` +
            `👤 ${topResult.author}\n` +
            `⏱️ ${topResult.duration} | 👁️ ${topResult.view}\n\n` +
            `🔄 *Starting download...*`,
            { quoted: message.data }
          )

          // Set videoId to the first result and continue with download
          const videoId = topResult.id

          // Continue with download process using the first result
          return await downloadVideo(message, videoId, topResult)

        } else {
          // Show selection list
          const msg = generateList(
            result.slice(0, 10).map(({ title, id, duration, view, author }) => ({
              text: `🎬 ${title}\n⏱️ ${duration} | 👁️ ${view}\n👤 ${author}\n`,
              id: `ytdl https://www.youtube.com/watch?v=${id}`,
            })),
            `🔍 *Search Results for:* ${match}\n\n` +
            `💡 *Tip:* Add "auto" to search term for instant download\n` +
            `Example: \`.ytdl ${match} auto\`\n\n` +
            `Select a video to download:`,
            message.jid,
            message.participant,
            message.id
          )
          return await message.send(msg.message, { quoted: message.data }, msg.type)
        }
      } catch (error) {
        return await message.send(`❌ *Search failed:* ${error.message}`)
      }
    }

    const videoId = vid[1]
    const videoUrl = `https://www.youtube.com/watch?v=${videoId}`
    
    // Get video info first
    let videoInfo
    try {
      const [info] = await yts(videoId, true, null, message.id)
      videoInfo = info
    } catch (error) {
      return await message.send(`❌ *Failed to get video info:* ${error.message}`)
    }

    const { title, duration, view, author, thumbnail } = videoInfo
    
    // Send initial message with video info
    await message.send(
      `📥 *Downloading Video...*\n\n` +
      `🎬 *Title:* ${title}\n` +
      `⏱️ *Duration:* ${duration}\n` +
      `👁️ *Views:* ${view}\n` +
      `👤 *Author:* ${author}\n\n` +
      `🔄 *Processing... Please wait*`,
      { quoted: message.data }
    )

    // Method 1: Try Y2mate
    try {
      await message.send('🔄 *Trying Method 1: Y2mate...*')
      
      const y2mateResult = await y2mate.get(videoId, 'video')
      
      if (isUrl(y2mateResult)) {
        await message.send('✅ *Method 1 successful! Sending video...*')
        return await message.sendFromUrl(y2mateResult, { 
          quoted: message.data,
          caption: `🎬 *${title}*\n👤 ${author}\n⏱️ ${duration}`
        })
      }
      
      if (y2mateResult && y2mateResult.video) {
        // Get the best quality available
        const qualities = Object.keys(y2mateResult.video)
        const bestQuality = qualities.includes('720p') ? '720p' : 
                           qualities.includes('480p') ? '480p' : 
                           qualities.includes('360p') ? '360p' : qualities[0]
        
        if (bestQuality) {
          const downloadUrl = await y2mate.dl(videoId, 'video', bestQuality)
          if (downloadUrl) {
            await message.send('✅ *Method 1 successful! Sending video...*')
            return await message.sendFromUrl(downloadUrl, { 
              quoted: message.data,
              caption: `🎬 *${title}*\n👤 ${author}\n⏱️ ${duration}\n📺 Quality: ${bestQuality}`
            })
          }
        }
      }
    } catch (error) {
      await message.send(`❌ *Method 1 failed:* ${error.message}`)
    }

    // Method 2: Try alternative API
    try {
      await message.send('🔄 *Trying Method 2: Alternative API...*')
      
      const apiUrl = `https://api.cobalt.tools/api/json`
      const response = await axios.post(apiUrl, {
        url: videoUrl,
        vQuality: '720',
        vFormat: 'mp4',
        isAudioOnly: false,
        isNoTTWatermark: false,
        isTTFullAudio: false
      }, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        timeout: 30000
      })

      if (response.data && response.data.url) {
        await message.send('✅ *Method 2 successful! Sending video...*')
        return await message.sendFromUrl(response.data.url, { 
          quoted: message.data,
          caption: `🎬 *${title}*\n👤 ${author}\n⏱️ ${duration}\n🔧 Method: Cobalt API`
        })
      }
    } catch (error) {
      await message.send(`❌ *Method 2 failed:* ${error.message}`)
    }

    // Method 3: Try another alternative
    try {
      await message.send('🔄 *Trying Method 3: SaveFrom API...*')
      
      const saveFromUrl = `https://worker-savefrom-net.herokuapp.com/api/convert?url=${encodeURIComponent(videoUrl)}`
      const response = await axios.get(saveFromUrl, { timeout: 30000 })

      if (response.data && response.data.url && response.data.url.length > 0) {
        const videoData = response.data.url.find(item => item.type === 'video' && item.quality)
        if (videoData) {
          await message.send('✅ *Method 3 successful! Sending video...*')
          return await message.sendFromUrl(videoData.url, { 
            quoted: message.data,
            caption: `🎬 *${title}*\n👤 ${author}\n⏱️ ${duration}\n📺 Quality: ${videoData.quality}\n🔧 Method: SaveFrom`
          })
        }
      }
    } catch (error) {
      await message.send(`❌ *Method 3 failed:* ${error.message}`)
    }

    // If all methods fail, provide helpful message
    return await message.send(
      `❌ *All download methods failed*\n\n` +
      `🔧 *Alternative options:*\n` +
      `• Try again in a few minutes\n` +
      `• Use \`.yta ${videoUrl}\` for audio only\n` +
      `• Check if the video is available in your region\n` +
      `• Try a different video\n\n` +
      `📋 *Video Info:*\n` +
      `🎬 ${title}\n` +
      `👤 ${author}\n` +
      `⏱️ ${duration}\n` +
      `🔗 ${videoUrl}`,
      { quoted: message.data }
    )
  }
)

// Enhanced YouTube Audio Downloader
bot(
  {
    pattern: 'ytmp3 ?(.*)',
    desc: 'Enhanced YouTube audio downloader',
    type: 'download',
  },
  async (message, match) => {
    match = match || message.reply_message.text
    if (!match) return await message.send('*🎵 Enhanced YouTube Audio Downloader*\n\n*Usage:*\n• `.ytmp3 <YouTube URL>`\n• `.ytmp3 <search term>`')

    const vid = ytIdRegex.exec(match)
    
    // If not a direct URL, search first
    if (!vid) {
      try {
        const result = await yts(match, false, null, message.id)
        if (!result.length) return await message.send(`❌ *No results found for:* ${match}`)
        
        const msg = generateList(
          result.slice(0, 10).map(({ title, id, duration, view, author }) => ({
            text: `🎵 ${title}\n⏱️ ${duration} | 👁️ ${view}\n👤 ${author}\n`,
            id: `ytmp3 https://www.youtube.com/watch?v=${id}`,
          })),
          `🔍 *Search Results for:* ${match}\n\nSelect a song to download:`,
          message.jid,
          message.participant,
          message.id
        )
        return await message.send(msg.message, { quoted: message.data }, msg.type)
      } catch (error) {
        return await message.send(`❌ *Search failed:* ${error.message}`)
      }
    }

    const videoId = vid[1]
    const videoUrl = `https://www.youtube.com/watch?v=${videoId}`
    
    // Get video info
    let videoInfo
    try {
      const [info] = await yts(videoId, true, null, message.id)
      videoInfo = info
    } catch (error) {
      return await message.send(`❌ *Failed to get video info:* ${error.message}`)
    }

    const { title, duration, author, thumbnail } = videoInfo
    
    await message.send(
      `🎵 *Downloading Audio...*\n\n` +
      `🎵 *Title:* ${title}\n` +
      `⏱️ *Duration:* ${duration}\n` +
      `👤 *Author:* ${author}\n\n` +
      `🔄 *Processing... Please wait*`,
      { quoted: message.data }
    )

    // Try Y2mate for audio
    try {
      const audio = await y2mate.get(videoId, 'audio')
      
      if (isUrl(audio)) {
        return await message.sendFromUrl(audio, { 
          quoted: message.data,
          mimetype: 'audio/mpeg',
          fileName: `${title}.mp3`
        })
      }
      
      const result = await y2mate.dl(videoId, 'audio')
      if (result) {
        const { buffer } = await getBuffer(result)
        if (buffer) {
          return await message.send(
            buffer,
            { 
              quoted: message.data, 
              mimetype: 'audio/mpeg',
              fileName: `${title}.mp3`
            },
            'audio'
          )
        }
        return await message.sendFromUrl(result, { 
          quoted: message.data,
          mimetype: 'audio/mpeg',
          fileName: `${title}.mp3`
        })
      }
    } catch (error) {
      return await message.send(`❌ *Audio download failed:* ${error.message}`)
    }
  }
)
