/**
 * Local YouTube Download Fix
 * 
 * This plugin focuses on using only the local y2mate library
 * since external APIs are not accessible from your network.
 */

const { bot, yts, y2mate, getBuffer, isUrl, generateList } = require('../lib/')

const ytIdRegex = /(?:http(?:s|):\/\/|)(?:(?:www\.|)youtube(?:\-nocookie|)\.com\/(?:watch\?.*(?:|\&)v=|embed|shorts\/|v\/)|youtu\.be\/)([-_0-9A-Za-z]{11})/

// Local YouTube Video Downloader (using only y2mate library)
bot(
  {
    pattern: 'ytlocal ?(.*)',
    desc: 'Local YouTube video downloader using y2mate library only',
    type: 'download',
  },
  async (message, match) => {
    match = match || message.reply_message.text
    if (!match) return await message.send('*📥 Local YouTube Video Downloader*\n\n*Usage:*\n• `.ytlocal <YouTube URL>`\n• `.ytlocal <search term>`\n• `.ytlocal <search term> auto` - Auto download first result')

    const vid = ytIdRegex.exec(match)
    
    // If not a direct URL, search first
    if (!vid) {
      try {
        const result = await yts(match, false, null, message.id)
        if (!result.length) return await message.send(`❌ *No results found for:* ${match}`)
        
        // Check if user wants auto-download
        if (match.includes(' auto') || match.includes(' first') || match.includes(' download')) {
          const searchTerm = match.replace(/ (auto|first|download)/g, '').trim()
          const topResult = result[0]
          
          await message.send(
            `🎯 *Auto-downloading first result for:* ${searchTerm}\n\n` +
            `🎬 *${topResult.title}*\n` +
            `👤 ${topResult.author}\n` +
            `⏱️ ${topResult.duration}\n\n` +
            `🔄 *Starting download...*`,
            { quoted: message.data }
          )
          
          return await downloadVideoLocal(message, topResult.id, topResult)
        } else {
          const msg = generateList(
            result.slice(0, 10).map(({ title, id, duration, view, author }) => ({
              text: `🎬 ${title}\n⏱️ ${duration} | 👁️ ${view}\n👤 ${author}\n`,
              id: `ytlocal https://www.youtube.com/watch?v=${id}`,
            })),
            `🔍 *Search Results for:* ${match}\n\nSelect a video to download:`,
            message.jid,
            message.participant,
            message.id
          )
          return await message.send(msg.message, { quoted: message.data }, msg.type)
        }
      } catch (error) {
        return await message.send(`❌ *Search failed:* ${error.message}`)
      }
    }

    const videoId = vid[1]
    
    // Get video info
    let videoInfo
    try {
      const [info] = await yts(videoId, true, null, message.id)
      videoInfo = info
    } catch (error) {
      return await message.send(`❌ *Failed to get video info:* ${error.message}`)
    }

    return await downloadVideoLocal(message, videoId, videoInfo)
  }
)

// Local audio downloader
bot(
  {
    pattern: 'ytaudio ?(.*)',
    desc: 'Local YouTube audio downloader using y2mate library only',
    type: 'download',
  },
  async (message, match) => {
    match = match || message.reply_message.text
    if (!match) return await message.send('*🎵 Local YouTube Audio Downloader*\n\n*Usage:*\n• `.ytaudio <YouTube URL>`\n• `.ytaudio <search term>`\n• `.ytaudio <search term> auto` - Auto download first result')

    const vid = ytIdRegex.exec(match)
    
    if (!vid) {
      try {
        const result = await yts(match, false, null, message.id)
        if (!result.length) return await message.send(`❌ *No results found for:* ${match}`)
        
        if (match.includes(' auto') || match.includes(' first') || match.includes(' download')) {
          const topResult = result[0]
          return await downloadAudioLocal(message, topResult.id, topResult)
        } else {
          const msg = generateList(
            result.slice(0, 10).map(({ title, id, duration, view, author }) => ({
              text: `🎵 ${title}\n⏱️ ${duration} | 👁️ ${view}\n👤 ${author}\n`,
              id: `ytaudio https://www.youtube.com/watch?v=${id}`,
            })),
            `🔍 *Search Results for:* ${match}\n\nSelect audio to download:`,
            message.jid,
            message.participant,
            message.id
          )
          return await message.send(msg.message, { quoted: message.data }, msg.type)
        }
      } catch (error) {
        return await message.send(`❌ *Search failed:* ${error.message}`)
      }
    }

    const videoId = vid[1]
    let videoInfo
    try {
      const [info] = await yts(videoId, true, null, message.id)
      videoInfo = info
    } catch (error) {
      return await message.send(`❌ *Failed to get video info:* ${error.message}`)
    }

    return await downloadAudioLocal(message, videoId, videoInfo)
  }
)

// Local video download function
async function downloadVideoLocal(message, videoId, videoInfo) {
  const { title, duration, author } = videoInfo
  
  const safeTitle = title || 'Unknown Title'
  const safeDuration = duration || 'Unknown'
  const safeAuthor = author || 'Unknown Channel'

  await message.send(
    `📥 *Local Video Download*\n\n` +
    `🎬 *Title:* ${safeTitle}\n` +
    `⏱️ *Duration:* ${safeDuration}\n` +
    `👤 *Author:* ${safeAuthor}\n\n` +
    `🔄 *Using local y2mate library...*`,
    { quoted: message.data }
  )

  try {
    // Try different video qualities
    const qualities = ['720', '480', '360', 'auto']
    
    for (const quality of qualities) {
      try {
        await message.send(`🔄 *Trying ${quality}p quality...*`)
        
        const result = await y2mate.get(videoId, 'video', quality)
        
        if (isUrl(result)) {
          await message.send(`✅ *Success! Sending ${quality}p video...*`)
          return await message.sendFromUrl(result, {
            quoted: message.data,
            caption: `🎬 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n📺 Quality: ${quality}p`
          })
        }
        
        // Try download method
        const downloadResult = await y2mate.dl(videoId, 'video', quality)
        if (downloadResult && isUrl(downloadResult)) {
          await message.send(`✅ *Success! Sending ${quality}p video...*`)
          return await message.sendFromUrl(downloadResult, {
            quoted: message.data,
            caption: `🎬 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n📺 Quality: ${quality}p`
          })
        }
        
      } catch (qualityError) {
        await message.send(`❌ *${quality}p failed: ${qualityError.message}*`)
        continue
      }
    }
    
    throw new Error('All quality options failed')
    
  } catch (error) {
    return await message.send(
      `❌ *Local download failed*\n\n` +
      `🔧 *Error:* ${error.message}\n\n` +
      `💡 *Try:*\n` +
      `• Different video\n` +
      `• Wait and try again\n` +
      `• Use \`.ytaudio\` for audio only`,
      { quoted: message.data }
    )
  }
}

// Local audio download function
async function downloadAudioLocal(message, videoId, videoInfo) {
  const { title, duration, author } = videoInfo
  
  const safeTitle = title || 'Unknown Title'
  const safeDuration = duration || 'Unknown'
  const safeAuthor = author || 'Unknown Channel'

  await message.send(
    `🎵 *Local Audio Download*\n\n` +
    `🎵 *Title:* ${safeTitle}\n` +
    `⏱️ *Duration:* ${safeDuration}\n` +
    `👤 *Author:* ${safeAuthor}\n\n` +
    `🔄 *Using local y2mate library...*`,
    { quoted: message.data }
  )

  try {
    const result = await y2mate.get(videoId, 'audio')
    
    if (isUrl(result)) {
      await message.send('✅ *Success! Sending audio...*')
      return await message.sendFromUrl(result, {
        quoted: message.data,
        mimetype: 'audio/mpeg',
        fileName: `${safeTitle}.mp3`
      })
    }
    
    const downloadResult = await y2mate.dl(videoId, 'audio')
    if (downloadResult && isUrl(downloadResult)) {
      await message.send('✅ *Success! Sending audio...*')
      return await message.sendFromUrl(downloadResult, {
        quoted: message.data,
        mimetype: 'audio/mpeg',
        fileName: `${safeTitle}.mp3`
      })
    }
    
    throw new Error('No download URL returned')
    
  } catch (error) {
    return await message.send(
      `❌ *Local audio download failed*\n\n` +
      `🔧 *Error:* ${error.message}\n\n` +
      `💡 *Try:*\n` +
      `• Different video\n` +
      `• Wait and try again\n` +
      `• Use original \`.yta\` command`,
      { quoted: message.data }
    )
  }
}
