{"extra": {"init_session": "[{0}] <PERSON><PERSON><PERSON> se<PERSON> baru", "load_session": "[{0}] Memeriksa status sesi", "invalid_session": "[{0}] {1} ID SESI TIDAK VALID, SILAKAN SCAN LAGI!!!", "success_session": "[{0}] <PERSON><PERSON> ber<PERSON> di<PERSON>.", "connecting": "[{0}] Menghubungkan...", "connected": "[{0}] Terhubung {1}", "instance_close": "[{0}] <PERSON><PERSON><PERSON><PERSON> di<PERSON>p", "instance_restart": "[{0}] Instance sedang dimulai ulang...", "reconnect": "[{0}] Menghubungkan ulang...({1})", "reconnect_after": "[{0}] Menghubungkan ulang setelah 1 menit", "another_login": "[{0}] Sesi login di perangkat lain.", "error_message": "```---<PERSON><PERSON><PERSON><PERSON> ERROR---\n\nVersi : {0}\n<PERSON><PERSON> : {1}\nError   : {2}\nJid     : {3}\nPer<PERSON>ah : {4}\nPlatform : {5}```\n\n```-----``` *Dibuat oleh LyFE dengan ❣* ```-----```", "deleted_message": "           <PERSON><PERSON>", "install_external_plugin": "[{0}] Menginstal plugin eksternal...", "installed_external_plugin": "[{0}] <PERSON><PERSON>in Eksternal Terinstal", "plugin_install": "[{0}] Menginstal Plugin...", "plugin_installed": "[{0}] {1} Terinstal", "plugins_installed": "[{0}] <PERSON><PERSON><PERSON>", "plugin_install_error": "[{0}] Error saat menginstal {1}, menghapus plugin", "plugin_not_found": "[{0}] plugin {1} t<PERSON><PERSON> (404), menghapus plugin", "group_cmd": "<PERSON><PERSON><PERSON> ini hanya tersedia di grup."}, "plugins": {"common": {"reply_to_message": "<PERSON><PERSON> ke pesan", "not_admin": "<PERSON><PERSON> bukan admin.", "reply_to_image": "Balas ke gambar", "update": "_Pengaturan berhasil diperbarui! Preferensi Anda telah disimpan dan sekarang aktif._"}, "menu": {"help": "```╭────────────────╮\n      ʟᴇᴠᴀɴᴛᴇʀ\n╰────────────────╯\n\n╭────────────────\n│ Prefix : {0}\n│ Pengguna : {1}\n│ <PERSON>ak<PERSON> : {2}\n│ <PERSON> : {3}\n│ <PERSON><PERSON> : {4}\n│ Versi : {5}\n│ Plugin : {6}\n│ Ram : {7}\n│ Uptime : {8}\n│ Platform : {9}\n╰────────────────```", "menu": "```╭═══ LEVANTER ═══⊷\n┃❃╭──────────────\n┃❃│ Prefix : {0}\n┃❃│ Pengguna : {1}\n┃❃│ Waktu : {2}\n┃❃│ Hari : {3}\n┃❃│ Tanggal : {4}\n┃❃│ Versi : {5}\n┃❃│ Plugin : {6}\n┃❃│ Ram : {7}\n┃❃│ Uptime : {8}\n┃❃│ Platform : {9}\n┃❃╰───────────────\n╰═════════════════⊷```"}, "afk": {"example": "> *Cara Pakai AFK:*\n- Set AFK: *afk [alasan]*\n- Contoh: *afk Saya sibuk* Terakhir terlihat #lastseen yang lalu\n- Mengirim pesan otomatis menghapus status AFK\n- Matikan AFK: *afk off*", "not_afk": "Anda tidak lagi AFK.", "desc": "Set status AFK (Away From Keyboard)"}, "alive": {"default": "<PERSON><PERSON> hidup\nUptime : #uptime", "desc": "Menampilkan pesan status hidup bot dengan teks kustom opsional."}, "antifake": {"example": "*Status Antifake:* {0}\n\n> *<PERSON><PERSON><PERSON>:*\n- *antifake list* - Lihat kode negara\n- *antifake !91,1* - Izinkan/Tidak izinkan kode negara tertentu\n- *antifake on | off* - Aktifkan/Nonaktifkan antifake", "desc": "Aktifkan atau konfigurasi anti-nomor palsu", "not": "Tidak ada kode negara yang ditam<PERSON>lkan.", "status": "Antifake sekarang *{0}*.", "update": "> Antifake Diperbarui\n*Diizinkan:* {0}\n*Tidak Diizinkan:* {1}"}, "antilink": {"desc": "Aktifkan atau konfigurasi anti-link", "disable": "_Antilink sudah dinonaktifkan._", "antilink_notset": "Antilink belum dikonfigurasi.", "status": "Antilink sekarang *{0}*.", "info": "`> Status Antilink: {0}\n*URL yang <PERSON>:* {1}\n *Aksi :* {2}", "action_invalid": "*Aksi yang dimasukkan tidak valid.*", "action_update": "Aksi Antilink diperbarui menjadi: *{0}*", "update": "> Antilink Diperbarui\n*Diizinkan:* {0}\n*Tidak Diizinkan:* {1}", "example": "Status Antilink: *{0}*\n\n> <PERSON><PERSON><PERSON>:\n- antilink info - Lihat pengaturan saat ini\n- antilink whatsapp.com - Izinkan URL tertentu\n- antilink on | off - Aktifkan/Nonaktifkan antilink\n- antilink action/<kick | warn | null> - Atur aksi untuk link"}, "antiword": {"desc": "Filter kata-kata tertentu di grup chat", "example": "`Status AntiWord: {0}\n> *<PERSON><PERSON><PERSON>:*\n- antiword action/<kick | warn | null>* - Atur aksi untuk pelanggaran\n- antiword on | off - Aktifkan/Nonaktifkan filter kata\n- setvar ANTIWORDS:word1,word2,... - Tentukan kata yang diblokir", "action_update": "<PERSON><PERSON><PERSON> diperbarui menjadi: *{0}*", "status": "<PERSON><PERSON><PERSON> se<PERSON> *{0}*."}, "apk": {"desc": "Unduh APK dari APKMirror", "example": "> *<PERSON><PERSON><PERSON>:*\n- apk Mixplorer\n- apk whatsapp,apkm (termasuk bundle APK)", "no_result": "_Tidak ada hasil yang ditemukan untuk pencarian <PERSON>._", "apps_list": "Ap<PERSON><PERSON> yang <PERSON> ({0})"}, "delete": {"desc": "Anti-hapus: <PERSON><PERSON><PERSON><PERSON> pesan yang di<PERSON>pus", "example": "> *<PERSON><PERSON><PERSON>:*\n- delete p - <PERSON><PERSON> pesan yang dihapus ke chat/sudo <PERSON>a\n- delete g - <PERSON><PERSON> pesan yang dihapus di grup yang sama\n- delete off - Nonaktifkan anti-hapus\n- delete <jid> - <PERSON><PERSON> pesan yang dihapus ke JID tertentu", "invalid_jid": "*Error:* _<PERSON><PERSON> tidak valid_", "dlt_msg_jid": " _Pesan yang dihapus akan dikirim ke: {0}_", "dlt_msg_disable": "Anti-hapus telah dinonaktif<PERSON>.", "dlt_msg_sudo": "_P<PERSON> yang di<PERSON>pus akan dikirim ke chat atau sudo <PERSON>._", "dlt_msg_chat": "_P<PERSON> yang di<PERSON>pus akan dikirim ke chat itu sendiri._"}, "dlt": {"desc": "hapus pesan yang dibalas"}, "fb": {"desc": "Unduh video Facebook", "example": "", "quality": "<PERSON><PERSON><PERSON>", "invalid": "*Error:* _Tidak ada video yang ditemukan untuk URL yang diberikan._"}, "fancy": {"desc": "Buat teks mewah dari teks yang diberikan", "example": "> *<PERSON>:*\nfancy <teks>\nfancy <nomor_font> (bala<PERSON> ke pesan)\n\n*Contoh:*\n- fancy Halo\n- fancy 7 (saat membalas pesan)", "invalid": "*Nomor Font Tidak Valid!*\nMasukkan angka antara *1-47*."}, "stop": {"desc": "Hapus filter di chat", "example": "> *<PERSON>:*\n- stop <filter>\n- stop hi", "delete": "{0} dihapus", "not_found": "_{0} tidak ditemukan di filter._"}, "filter": {"desc": "Kelola filter di grup", "example": "> *Contoh:*\n- filter hi (saat membalas pesan teks)\n- filter list (<PERSON><PERSON><PERSON>an filter saat ini)", "list": "> *Filter Saat Ini:*\n{0}", "filter_add": "*{0}* filter ditambahkan"}, "forward": {"desc": "Teruskan pesan yang dibalas ke JID tertentu", "foward": "<PERSON><PERSON> ke: {0}", "example": "JID tidak valid!\n> *<PERSON>:*\n- forward <jid>\n- forward <EMAIL>"}, "save": {"desc": "Teruskan pesan yang dibalas ke diri sendiri", "save": "P<PERSON> disimpan!"}, "gemini": {"desc": "Google Gemini AI - <PERSON> apa saja!", "example": "> *Contoh :*\n- gemini hai\n- gemini apa yang ada di gambar (saat membalas gambar)", "Key": "> Kunci API Gemini Hilang!\nDapatkan di: https://aistudio.google.com/app/apikey\n\n*Atur menggunakan:*\nsetvar GEMINI_API_KEY = kunci_api_anda"}, "gstop": {"desc": "Hapus filter global di semua grup", "example": "> *<PERSON>:*\n- gstop <filter>\n- gstop hi", "delete": "{0} dihapus", "not_found": "_{0} tidak ditemukan di filter global._"}, "pstop": {"desc": "Hapus filter pribadi di semua grup", "example": "> *<PERSON>:*\n- pstop <filter>\n- pstop hi", "delete": "{0} dihapus", "not_found": "_{0} tidak ditemukan di filter pribadi._"}, "gfilter": {"desc": "Kelola filter global di grup", "example": "> *Contoh :*\n- gfilter hi (saat membalas pesan teks)\n- gfilter list (Tam<PERSON><PERSON><PERSON> filter global saat ini)", "add": "*{0}* filter global ditambahkan"}, "pfilter": {"desc": "Kelola filter global di chat pribadi", "example": "> *Contoh :*\n- pfilter hi (saat membalas pesan teks)\n- pfilter list (Tampilkan filter pribadi saat ini)", "add": "*{0}* filter pribadi ditambahkan"}, "gpp": {"desc": "ubah ikon grup", "update": "_Ikon grup diperbarui_"}, "greet": {"setdesc": "<PERSON>ur pesan sambutan personal", "setexample": "> *Contoh:* setg<PERSON>, ini adalah bot. Bos saya akan membalas segera.", "setupdate": "_P<PERSON> sambutan telah diperbarui._", "getdesc": "<PERSON>bil pesan sambutan personal", "notsetgreet": "> Tidak ada pesan sambutan yang diatur.", "deldesc": "Hapus pesan sambutan personal", "delupdate": "<PERSON><PERSON> sambutan telah di<PERSON>."}, "greetings": {"welcome_desc": "<PERSON><PERSON> pesan selamat datang ke anggota baru", "welcome_example": "Selamat datang saat ini {0}\n\nUntuk detail lebih lanjut, kunjungi: https://levanter-plugins.vercel.app/faq", "welcome_enable": "_Selamat datang sekarang diaktifkan_", "welcome_disable": "_Selamat datang sekarang dinonaktifkan_", "welcome_delete": "_Pesan selamat datang dihapus_", "goodbye_desc": "<PERSON><PERSON> pesan selamat tinggal ke anggota", "goodbye_example": "Selamat tinggal saat ini {0}\n\nUntuk detail lebih lanjut, kunjungi: https://levanter-plugins.vercel.app/faq", "goodbye_enable": "_Selamat tinggal sekarang diaktifkan_", "goodbye_disable": "_Selamat tinggal sekarang dinonaktifkan_", "goodbye_delete": "_P<PERSON> selamat tinggal di<PERSON>pus_"}, "groq": {"example": "*Contoh:* groq Hai\n\nAnda bisa mengatur variabel lingkungan berikut:\n- GROQ_API_KEY\n- GROQ_MODEL\n- GROQ_SYSTEM_MSG\n\nUntuk detail lebih lanjut, kunjungi: https://console.groq.com/keys", "desc": "Berinteraksi dengan GROQ AI"}, "kick": {"desc": "Hapus anggota dari G<PERSON>.", "not_admin": "<PERSON><PERSON> bukan admin, jadi tidak bisa mengh<PERSON>us anggota.", "mention_user": "<PERSON><PERSON>an sebutkan pengguna atau balas pesan mereka.", "admin": "<PERSON><PERSON><PERSON> yang dimaksud adalah admin dan tidak bisa dihapus.", "kicking_all": "<PERSON><PERSON><PERSON><PERSON> semua anggota non-admin... ({0} anggota). <PERSON><PERSON> ulang bot jika <PERSON>a ingin ber<PERSON>."}, "add": {"desc": "tambahkan anggota ke grup", "warning": "> Hindari menambahkan nomor yang tidak disimpan di kontak; ini bisa meningkatkan risiko terkena ban.", "not_admin": "<PERSON><PERSON> bukan admin, jadi tidak bisa menambahkan anggota.", "invalid_number": "Silakan berikan nomor telepon yang valid. Contoh: add 91987654321", "failed": "Gagal menambahkan. Undangan telah dikirim sebagai gantinya."}, "promote": {"desc": "Berikan peran admin", "not_admin": "<PERSON><PERSON> bukan admin, jadi tidak bisa mengubah peran.", "mention_user": "<PERSON><PERSON>an sebutkan pengguna atau balas pesan mereka.", "already_admin": "Pengguna sudah menjadi admin."}, "demote": {"desc": "<PERSON><PERSON> peran admin", "not_admin": "<PERSON><PERSON> bukan admin, jadi tidak bisa mengubah peran.", "mention_user": "<PERSON><PERSON>an sebutkan pengguna atau balas pesan mereka.", "not_admin_user": "Pengguna bukan admin."}, "invite": {"desc": "Dapatkan tautan undangan grup", "not_admin": "<PERSON><PERSON> bukan admin, jadi tidak bisa membuat tautan undangan.", "success": "Ini tautan undangan grup:\n{0}"}, "mute": {"desc": "Jadikan grup hanya untuk admin", "not_admin": "<PERSON><PERSON> b<PERSON> admin, jadi tidak bisa mengubah pengaturan grup.", "mute": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>a {0} menit."}, "unmute": {"desc": "Jadikan grup semua peserta bisa mengirim pesan", "not_admin": "<PERSON><PERSON> b<PERSON> admin, jadi tidak bisa mengubah pengaturan grup."}, "join": {"desc": "Bergabung dengan grup menggunakan tautan undangan", "invalid_link": "<PERSON>lakan berikan tautan undangan grup WhatsApp yang valid.", "group_full": "Grup sudah penuh dan tidak bisa menerima anggota baru.", "success": "<PERSON><PERSON><PERSON><PERSON> bergabung ke grup.", "request_sent": "Permintaan bergabung dikirim."}, "revoke": {"desc": "<PERSON><PERSON><PERSON> tautan undangan grup", "not_admin": "<PERSON><PERSON> bukan admin, jadi tidak bisa mencabut tautan undangan."}, "group_info": {"desc": "Tampilkan info tautan undangan grup", "invalid_link": "<PERSON>lakan berikan tautan undangan WhatsApp yang valid.", "details": "*Nama:* {0}\n*ID Grup:* {1}@g.us\n*Pemilik:* {2}\n*Anggota:* {3}\n*Dibuat pada:* {4}\n*Deskripsi:* {5}"}, "common_members": {"desc": "<PERSON><PERSON><PERSON><PERSON> atau hapus anggota yang sama di dua grup atau lebih", "found": "Ditemukan 0 anggota yang sama."}, "insta": {"usage": "Contoh: insta <URL Instagram>", "not_found": "Tidak ditemukan.", "desc": "Undu<PERSON>an, reel, dan video Instagram."}, "ison": {"usage": "Contoh: ison <nomor telepon>", "not_exist": "`*Tidak Ada di WhatsApp* ({0})\n`", "exist": "\n*Ada di WhatsApp* ({0})\n", "privacy": "*Pengaturan Privasi Aktif* ({0})\n", "desc": "Periksa apakah nomor telepon terdaftar di WhatsApp."}, "lydia": {"usage": "Cara Pakai: lydia on | off\nBalas atau sebutkan untuk mengaktifkan untuk pengguna tertentu.", "activated": "<PERSON> telah <PERSON>.", "deactivated": "<PERSON> telah din<PERSON>.", "note": "Ini hanya bekerja dari pesan balasan.", "desc": "Aktifkan atau nonaktifkan fitur chatbot."}, "rotate": {"usage": "Contoh: rotate right|left|flip (balas ke video).", "not_found": "<PERSON><PERSON><PERSON> balas ke video dan tentukan arah rotasi yang valid (kanan, kiri, atau flip).", "desc": "Putar video ke kanan, kiri, atau flip.", "convert": "_Mengonversi..._"}, "mp3": {"usage": "Balas ke video atau audio untuk mengonversinya ke MP3.", "not_found": "<PERSON><PERSON><PERSON> balas ke video atau pesan audio.", "desc": "Konversi video ke audio atau klip audio ke catatan suara."}, "photo": {"usage": "<PERSON>las ke stiker foto untuk mengonversinya ke gambar.", "desc": "Konversi stiker menjadi gambar."}, "reverse": {"usage": "Balas ke video atau audio untuk memutar balik pemutaran.", "not_found": "<PERSON><PERSON><PERSON> balas ke video atau pesan audio.", "desc": "Putar balik pemutaran video atau klip audio."}, "cut": {"usage": "Contoh: cut 0;30 (mulai;durasi) (balas ke video atau audio).", "not_found": "<PERSON><PERSON><PERSON> balas ke video atau audio dengan nilai mulai dan durasi yang valid (contoh: 10;30).", "desc": "Potong segmen dari file audio atau video."}, "trim": {"usage": "Contoh: trim 10;30 (balas ke video).", "not_found": "<PERSON><PERSON><PERSON> balas ke video dengan nilai mulai dan durasi yang valid (contoh: 60;30).", "desc": "Potong video antara waktu mulai dan durasi yang ditentukan."}, "page": {"usage": "Contoh: page 1 (balas ke gambar).", "not_found": "<PERSON><PERSON><PERSON> balas ke gambar dengan caption numerik yang menun<PERSON>kkan nomor halaman.", "desc": "Tambahkan gambar sebagai halaman ke dokumen PDF.", "add": "Halaman {0} ditambahkan!"}, "pdf": {"usage": "Contoh: pdf catatan (berikan judul untuk PDF).", "not_found": "Silakan berikan judul untuk dokumen PDF.", "desc": "Konversi gambar ke dokumen PDF."}, "merge": {"usage": "Contoh: merge 1 (balas dengan nomor urut ke video).", "not_found": "<PERSON><PERSON><PERSON> balas ke video dengan nomor urut yang valid.", "desc": "Gabung<PERSON> beberapa video menjadi satu.", "merge": "_Menggabungkan {0} video_", "add": "_Video {0} ditambahkan_"}, "compress": {"usage": "Balas ke video untuk mengompresnya.", "desc": "Kompres file video untuk mengurangi ukurannya."}, "bass": {"usage": "<PERSON><PERSON><PERSON>: bass 10 (balas ke audio atau video).", "desc": "Ubah level bass file audio."}, "treble": {"usage": "Contoh: treble 10 (balas ke audio atau video).", "desc": "Ubah level treble file audio."}, "histo": {"usage": "<PERSON>las ke audio atau video untuk <PERSON><PERSON><PERSON> video histogram.", "desc": "Konversi audio ke video histogram visual."}, "vector": {"usage": "Balas ke audio atau video untuk membuat video visualisasi vektor.", "desc": "Konversi audio ke video visualisasi vektor."}, "crop": {"usage": "Contoh: crop 512,512,0,512 (balas ke video).", "not_found": "<PERSON><PERSON><PERSON> balas ke video dengan dimensi crop yang valid dalam format: out_w,out_h,x,y.", "desc": "Crop video ke dimensi yang ditentukan.", "xcrop": "Lebar video: *{0}*, tinggi: *{1}*\nPilih ukuran output di antara."}, "low": {"usage": "Balas ke audio atau video untuk menurunkan pitch.", "desc": "Ubah pitch audio ke nada yang lebih rendah."}, "pitch": {"usage": "Balas ke audio atau video untuk menyesuaikan pitch.", "not_found": "<PERSON><PERSON><PERSON> balas ke pesan audio atau video.", "desc": "Sesuaikan pitch file audio."}, "avec": {"usage": "Balas ke audio atau video untuk mengonversinya ke format video.", "not_found": "<PERSON><PERSON><PERSON> balas ke pesan audio atau video.", "desc": "Konversi klip audio ke video."}, "avm": {"usage": "Balas dengan audio dan video untuk menggabungkannya.", "desc": "Gabungkan file audio dan video menjadi satu.", "audio_add": "_Audio ditambahkan!_", "video_add": "_Video ditambahkan!_"}, "black": {"usage": "Balas ke audio atau video untuk menghasilkan video dengan latar belakang hitam.", "desc": "Konversi klip audio ke video dengan latar belakang hitam."}, "mediafire": {"usage": "Contoh: mediafire <URL Mediafire>", "not_found": "File tidak ditemukan. Silakan verifikasi URL dan coba lagi.", "desc": "Unduh file dari Mediafire."}, "mention": {"usage": "Contoh: mention on | off | get\n(Balas ke pesan untuk menargetkan pengguna tertentu.)", "desc": "Konfigurasi dan kelola fitur mention untuk balasan otomatis.", "not_activated": "Balasan ke mention tidak diaktif<PERSON>.", "current_status": "Mention adalah {0}. Untuk detail, kunjungi: https://levanter-plugins.vercel.app/faq", "activated": "Balasan ke mention di<PERSON><PERSON><PERSON><PERSON>.", "deactivated": "Balasan ke mention dinonakt<PERSON><PERSON>.", "updated": "Pengaturan mention diperbarui."}, "status": {"usage": "<PERSON>: status on | off | no-dl | except-view <jid,...> | only-view <jid,...>", "desc": "Kelola secara otomatis pembacaan status WhatsApp."}, "call": {"usage": "<PERSON>: call on | off", "desc": "<PERSON><PERSON> panggilan masuk secara otomatis."}, "read": {"usage": "<PERSON>: read on | off", "desc": "Aktifkan atau nonaktifkan pembacaan otomatis pesan masuk."}, "online": {"usage": "<PERSON>ai: online on | off", "desc": "Jaga status akun Anda selalu online."}, "movie": {"usage": "Contoh: movie <judul film>", "not_found": "Film tidak ditemukan. <PERSON>lakan verifikasi judul dan coba lagi.", "desc": "Ambil informasi detail film, termasuk plot lengkap, dari API OMDB."}, "msgs": {"desc": "<PERSON><PERSON><PERSON><PERSON> jumlah pesan grup untuk setiap anggota, termasuk total individu dan durasi terakhir terlihat.", "msg_init": "\n*Nomor :* {0}\n*Nama :* {1}\n*Total Pesan :* {2}\n", "msg_last": "*<PERSON>rak<PERSON> :* {0} yang lalu\n"}, "reset": {"usage": "Contoh: reset all ATAU reset <balas/sebutkan>", "desc": "Reset jumlah pesan untuk seluruh grup atau anggota tertentu.", "reset_all": "<PERSON><PERSON><PERSON> pesan semua orang di<PERSON>pus", "reset_one": "_@{0} jumlah pesan di<PERSON>pus._"}, "inactive": {"usage": "> Contoh:\n-inactive day 10\n- inactive day 10 kick\n- inactive total 100\n- inactive total 100 kick\n- inactive day 7 total 150\n- inactive day 7 total 150 kick\n\njika kick tida<PERSON> disebutkan, hanya daftar", "desc": "Identifikasi atau hapus anggota yang tidak aktif berdasar<PERSON> jumlah pesan atau durasi tidak aktif. Tambahkan 'kick' untuk menghapus anggota.", "inactives": "_Total anggota tidak aktif adalah : {0}_", "removing": "_Menghapus {0} anggota tidak aktif dalam 7 detik_"}, "amute": {"usage": "<PERSON>: amute <jam> <menit>\n- amute on | off\n- amute info\n\nBalas dengan teks untuk mengatur pesan mute", "desc": "<PERSON><PERSON><PERSON>an pembisuan grup otomatis pada waktu tertentu dengan pesan kustom opsional.", "not_found": "Pengaturan AutoMute tidak ditemukan.", "already_disabled": "AutoMute sudah dinonaktifkan.", "enabled": "AutoMute Diaktifkan.", "disabled": "AutoMute Dinonaktifkan.", "invalid_format": "> Contoh:\n- amute 6 0\n- amute on | off\n- amute info\n\nBalas dengan teks untuk mengatur pesan mute.", "scheduled": "Grup akan dibisukan pada {0}\n*pesan :* {1}", "info": "Jam: {0}\nMenit: {1}\n<PERSON><PERSON><PERSON>: {2}\nMute: {3}\n<PERSON><PERSON>: {4}"}, "aunmute": {"usage": "<PERSON>: aunmute <jam> <menit>\n- aunmute on | off\n- aunmute info\nBalas dengan teks untuk mengatur pesan unmute", "desc": "<PERSON><PERSON><PERSON><PERSON> pembukaan grup otomatis pada waktu tertentu dengan pesan kustom opsional.", "not_found": "Pengaturan AutoUnMute tidak ditemukan.", "already_disabled": "AutoUnMute sudah dinonaktifkan.", "enabled": "AutoUnMute Diaktifkan.", "disabled": "AutoUnMute Dinonaktifkan.", "invalid_format": "> Contoh:\n- aunmute 16 30\n- aunmute on | off\n- aunmute info\n\nBalas dengan teks untuk mengatur pesan unmute.", "scheduled": "Grup akan dibuka pada {0}\n*pesan :* {1}", "info": "Jam: {0}\nMenit: {1}\n<PERSON><PERSON><PERSON>: {2}\nMute: {3}\n<PERSON><PERSON>: {4}"}, "zushi": {"usage": "> Contoh:\n- zushi ping, stiker\n- <PERSON>tuk mengatur semua perintah, ketik 'list' dan kemudian balas dengan pesan yang disalin (contoh: zushi pesan_yang_disalin).", "desc": "Memungkinkan Anda mengaktifkan perintah tertentu untuk digunakan oleh orang lain di chat.", "already_set": "{0} su<PERSON> dikon<PERSON>.", "allowed": "*perintah yang di<PERSON>an untuk* @{0}\n{1}"}, "yami": {"usage": "<PERSON><PERSON><PERSON> gunakan: yami", "desc": "<PERSON><PERSON><PERSON><PERSON> daftar perintah yang saat ini diizinkan di chat ini.", "not_set": "Belum ada perintah yang di<PERSON>an."}, "ope": {"usage": "Contoh: ope ping, stiker ATAU ope all", "desc": "<PERSON><PERSON> atau batalkan perintah yang di<PERSON>.", "not_found": "Tidak ada perintah yang diizinkan untuk {0}.", "all_removed": "<PERSON><PERSON><PERSON> perintah yang diizinkan telah di<PERSON>.", "removed": "*perintah yang dihapus untuk* @{0}\n{1}"}, "pdm": {"usage": "Cara Pakai: pdm on | off", "desc": "Aktifkan atau nonaktifkan notifikasi otomatis untuk event promote/demote di grup.", "not_found": "<PERSON>lakan tentukan 'on' atau 'off'. Contoh: pdm on", "activated": "Notifikasi promote/demote diaktifkan.", "deactivated": "Notifikasi promote/demote dinonaktifkan."}, "ping": {"desc": "<PERSON><PERSON><PERSON> waktu respons bot (latensi).", "ping_sent": "Ping!", "pong": "Pong! Waktu respons: {0} ms"}, "pinterest": {"usage": "Contoh: pinterest <URL Pinterest>", "not_found": "Tidak ada media yang ditemukan. Silakan periksa URL dan coba lagi.", "desc": "Unduh video atau gambar dari Pinterest."}, "plugin": {"usage": "> Contoh:\n- plugin <URL Gist>\n- plugin list", "desc": "Instal plugin eksternal dengan memberikan URL Gist yang berisi kode plugin, atau daftar semua plugin yang terinstal.", "invalid": "Silakan berikan URL plugin atau nama plugin yang valid.", "not_installed": "Tidak ada plugin yang terinstal saat ini.", "installed": "Plugin baru yang terinstal: {0}"}, "remove": {"usage": "> Contoh:\b- remove <nama_plugin>\n- remove all", "desc": "Hapus plugin eksternal dengan menentukan nama plugin atau hapus semua plugin yang terinstal.", "not_found": "Plugin *{0}* tidak ditem<PERSON>n.", "removed": "Plugin ber<PERSON><PERSON> di<PERSON>."}, "reboot": {"desc": "<PERSON><PERSON> instance bot menggunakan PM2.", "starting": "<PERSON><PERSON><PERSON>..."}, "fullpp": {"usage": "Contoh: fullpp (balas ke gambar)", "desc": "Atur foto profil ukuran penuh.", "updated": "Foto profil diperbarui."}, "jid": {"desc": "Mengembalikan JID pengguna atau chat. Ini memeriksa pengguna yang disebutkan, pesan balasan, atau default ke JID chat saat ini."}, "left": {"desc": "<PERSON><PERSON>ar dari grup saat ini. <PERSON><PERSON> ada teks tambahan, itu akan dikirim sebelum keluar."}, "block": {"usage": "Contoh: block (balas ke atau sebutkan pengguna)", "desc": "Blokir pengguna yang dimaksud.", "status": "Diblokir"}, "unblock": {"usage": "Contoh: unblock (balas ke atau sebutkan pengguna)", "desc": "<PERSON><PERSON> blokir pengguna yang dimaksud.", "status": "_<PERSON><PERSON><PERSON> blokir_"}, "pp": {"usage": "Contoh: pp (balas ke gambar)", "desc": "Perbarui foto profil <PERSON>a menggunakan gambar dari pesan yang dibalas."}, "whois": {"number": "*Nomor :* {0}", "name": "*Nama :* {0}", "about": "*Tentang :* {0}", "setAt": "*setAt :* {0}", "owner": "*Pemilik :* {0}", "members": "*Anggota* : {0}", "description": "*deskripsi* : {0}", "created": "*Dibuat* : {0}", "usage": "Contoh: whois <jid atau pengenal pengguna>", "desc": "<PERSON><PERSON><PERSON><PERSON> foto profil dan informasi tambahan (misalnya, tentang, status) dari pengguna atau grup."}, "gjid": {"desc": "Daftar semua JID grup beserta nama grupnya."}, "qr": {"usage": "Contoh: qr test  ATAU  balas ke gambar QR dengan qr", "desc": "Buat kode QR dari teks yang diberikan atau decode kode QR dari gambar yang dibalas."}, "reddit": {"usage": "Contoh: reddit <URL>", "desc": "Unduh video dari postingan Reddit yang ditentukan menggunakan URL yang diberikan.", "error": "Tidak ada video yang ditemukan untuk URL yang diberikan."}, "rmbg": {"usage": "Contoh: rmbg (balas ke gambar)", "desc": "Hapus latar belakang dari gambar yang dibalas menggunakan API remove.bg", "key": "Untuk menggunakan perintah ini, pastikan <PERSON>a telah mendaftar di remove.bg, memver<PERSON><PERSON><PERSON> akun <PERSON>, menyalin kunci API Anda, dan mengaturnya menggunakan .setvar RMBG_KEY:<kunci_api_anda> (contoh: .setvar RMBG_KEY:GWQ6jVy9MBpfYF9SnyG8jz8P). DAFTAR: https://accounts.kaleido.ai/users/sign_up | KUNCI API: https://www.remove.bg/dashboard#api-key", "error": "Kunci API atau gambar tidak ditemukan. Silakan atur kunci API Anda dan balas ke gambar."}, "setschedule": {"usage": "> *Contoh :*\n- setschedule jid,menit-jam-hari-bulan (dalam format 24 jam, hari dan bulan opsional)\n- setschedule <EMAIL>, 9-9-13-8\n- setschedule <EMAIL>, 0-10 (kirim pesan setiap hari jam 10 pagi)\n- setschedule <EMAIL>, 0-10, once (kirim pesan jam 10 pagi, satu kali)", "desc": "<PERSON><PERSON><PERSON><PERSON> pesan untuk dikirim secara otomatis pada waktu tertentu. Berikan JID target dan waktu (dalam format menit-jam-hari-bulan; hari dan bulan opsional). <PERSON>las ke pesan yang ingin <PERSON><PERSON> jadwalk<PERSON>.", "invalid": "Format atau waktu jadwal tidak valid. <PERSON>lakan ikuti salah satu contoh yang diberikan.", "no_reply": "<PERSON><PERSON><PERSON> balas ke pesan yang ingin Anda jadwalkan untuk dikirim.", "scheduled": "_<PERSON><PERSON><PERSON><PERSON><PERSON>an untuk dikirim pada_ *{0}* _di_ @{1}."}, "getschedule": {"desc": "<PERSON><PERSON> semua pesan yang dijad<PERSON>an untuk chat yang ditentukan.", "not_found": "Tidak ada pesan yang di<PERSON>.", "time": "Waktu : {0}"}, "delschedule": {"usage": "> Contoh:\n- delschedule <EMAIL>, 8-8-10-10\n- delschedule <EMAIL>\n- delschedule all", "desc": "<PERSON>pus pesan yang dijadwalkan dengan menentukan JID target dan waktu, atau hapus semua pesan yang dijadwalkan.", "invalid": "Format tidak valid. Silakan ikuti salah satu contoh.", "not_found": "<PERSON><PERSON><PERSON> tidak di<PERSON>.", "deleted": "_<PERSON><PERSON><PERSON> di<PERSON>._"}, "setstatus": {"usage": "Contoh: setstatus jid,jid,jid,... ATAU setstatus kontak", "desc": "Atur status WhatsApp untuk kontak tertentu atau untuk kontak yang diimpor. Balas ke pesan (teks, gambar, atau video) untuk mengatur status.", "reply_required": "<PERSON><PERSON><PERSON> balas ke pesan untuk mengatur status.", "sent": "_Status dikirim ke {0} kontak._"}, "scstatus": {"usage": "Contoh:\n- scstatus jid,jid,jid,...|menit-jam-hari-bulan (hari dan bulan opsional)\n- scstatus kontak|menit-jam-hari-bulan\n- scstatus delete all|menit-jam-hari-bulan\n- scstatus list", "desc": "Jadwalkan status WhatsApp untuk dikirim pada waktu tertentu. Gunakan 'kontak' untuk kontak yang diimpor atau berikan JID tertentu.", "reply_required": "<PERSON><PERSON><PERSON> balas ke pesan untuk menjadwalkan status.", "invalid": "Format tidak valid. Silakan ikuti salah satu contoh yang diberikan.", "scheduled": "_<PERSON><PERSON><PERSON><PERSON>an untuk dikirim pada_ {0}.", "list": "Daftar status yang dija<PERSON>an:", "deleted": "<PERSON><PERSON><PERSON>."}, "antispam": {"usage": "<PERSON>: antispam on | off", "desc": "Aktifkan atau nonaktifkan fitur AntiSpam untuk grup.", "activated": "AntiSpam diaktifkan.", "deactivated": "AntiSpam dinonaktifkan."}, "sticker": {"desc": "Konversi gambar atau video ke stiker. Balas ke pesan gambar atau video untuk membuat stiker.", "reply_required": "<PERSON><PERSON><PERSON> balas ke gambar atau video."}, "circle": {"desc": "<PERSON>n<PERSON><PERSON> gambar menjadi stiker lingkaran.", "reply_required": "<PERSON><PERSON><PERSON> balas ke gambar."}, "take": {"usage": "Contoh: take <judul,artis,url> (balas ke stiker atau audio). Untuk audio, judul diperlukan; artis dan URL opsional.", "desc": "Ubah paket stiker dengan memperbarui metadatanya. Jika membalas stiker, perbarui metadata paketnya. Jika membalas audio, tambahkan metadata (judul, artis, URL) ke file.", "reply_required": "<PERSON><PERSON><PERSON> balas ke pesan stiker atau audio.", "additional_info": "Untuk metadata audio, artis atau URL opsional."}, "mp4": {"desc": "Konversi stiker animasi (WebP) ke video MP4.", "reply_required": "<PERSON><PERSON><PERSON> balas ke stiker animasi."}, "story": {"usage": "Contoh: story <username> (atau balas ke pesan yang berisi username)", "desc": "Unduh story Instagram untuk username yang ditentukan. <PERSON><PERSON> ada beber<PERSON> story, daftar akan disediakan untuk dipilih.", "not_found": "Tidak ada story yang di<PERSON>ukan untuk username yang diberikan.", "list": "Total {0} story tersedia. <PERSON><PERSON>an pilih satu untuk diunduh.\n"}, "tag": {"usage": "Contoh: tag all | tag admin | tag notadmin | tag <pesan kustom> (atau balas ke pesan dengan 'tag')", "desc": "Tag anggota di grup berdasarkan pilihan Anda. Gunakan 'all' untuk menyebut semua anggota, 'admin' untuk menyebut hanya admin grup, 'notadmin' untuk menyebut anggota non-admin, atau berikan pesan kustom untuk dikirim bersama mention."}, "tictactoe": {"usage": "Contoh: tictactoe <jid_lawan> ATAU tictactoe restart <jid_lawan> ATAU tictactoe end", "desc": "Mainkan game TicTacToe melawan lawan. Tantang pengguna dengan menyebutkan mereka, membalas pesan mereka, atau menentukan JID mereka. Gunakan 'tictactoe end' untuk mengakhiri game dan 'tictactoe restart <jid_lawan>' untuk memulai ulang dengan lawan baru.", "choose_opponent": "<PERSON><PERSON><PERSON> pilih lawan dengan membalas pesan atau menyebutkan pengguna. Anda tidak bisa bermain melawan diri sendiri.", "game_ended": "Game berakhir.", "game_restarted": "Game dimulai ulang dengan lawan baru.", "invalid_input": "Input tidak valid. Silakan berikan lawan yang valid atau gunakan 'end' atau 'restart' dengan benar.", "players": "<PERSON><PERSON><PERSON>", "already_occupied": "_<PERSON><PERSON>_", "current_player": "pemainSaatIni", "game_finish": "Game Selesai 🏁", "winner": "<PERSON><PERSON><PERSON><PERSON>"}, "tiktok": {"usage": "Cara Pakai: tiktok <URL TikTok> (atau balas ke pesan dengan URL)", "desc": "Unduh video TikTok dari URL yang diberikan.", "not_found": "Video tidak ditemukan. <PERSON><PERSON>an verifikasi URL dan coba lagi."}, "tog": {"usage": "Contoh: tog ping off", "desc": "Aktifkan atau nonaktifkan perintah bot.", "invalid": "Input tidak valid. Gunakan: tog <perintah> on|off (contoh: tog ping off)", "self_reference": "<PERSON><PERSON><PERSON><PERSON> Anda benar-benar ingin mematikan saya?", "enabled": "{0} <PERSON><PERSON><PERSON><PERSON><PERSON>.", "disabled": "{0} Dinonaktifkan."}, "trt": {"usage": "Contoh: trt ml hai ATAU trt ml (balas ke pesan teks)", "desc": "Terjemahkan teks menggunakan Google Translate. Tentukan kode bahasa target (dan opsional kode bahasa sumber) saat membalas pesan."}, "twitter": {"usage": "Contoh: twitter <URL Twitter> (atau balas ke pesan yang berisi URL)", "desc": "Unduh video Twitter. <PERSON><PERSON> ada beberapa opsi kualitas, <PERSON><PERSON> akan diminta untuk memilih.", "not_found": "Tidak ada video yang ditemukan untuk URL Twitter yang diberikan.", "choose_quality": "> <PERSON><PERSON><PERSON>\n"}, "upload": {"usage": "Contoh: upload <URL> (atau balas ke pesan yang berisi URL)", "desc": "Unduh media dari URL yang diberikan. Untuk URL Google Images yang dipersingkat, URL gambar langsung akan diambil secara otomatis."}, "url": {"usage": "Contoh: url ATAU url imgur (balas ke gambar atau video)", "desc": "Konversi gambar atau video ke URL. Opsional, tentukan parameter (misalnya, 'imgur') untuk mendapatkan URL dari layanan tertentu."}, "getvar": {"usage": "Contoh: getvar sudo", "desc": "<PERSON><PERSON><PERSON><PERSON> nilai variabel. Berikan kunci variabel (case-insensitive) untuk mengambil nilainya.", "not_found": "{0} tidak ditemukan di variabel."}, "delvar": {"usage": "Contoh: del<PERSON> sudo", "desc": "<PERSON><PERSON> variabel dengan men<PERSON>ukan kun<PERSON>.", "not_found": "{0} tidak ditemukan di variabel.", "deleted": "{0} di<PERSON><PERSON>."}, "setvar": {"usage": "Contoh: setvar kunci = nilai", "desc": "Atur variabel dengan kunci dan nilai tertentu. Gunakan '=' untuk memisahkan kunci dari nilai.", "success": "Variabel baru {0} ditambahkan sebagai {1}."}, "allvar": {"desc": "<PERSON><PERSON><PERSON><PERSON> semua variabel yang disimpan dalam urutan terurut."}, "vote": {"usage": "> Contoh :\nvote q|Apa warna favoritmu?\no|😀|Biru\no|😊|Merah", "desc": "<PERSON><PERSON> voting di grup WhatsApp.", "notes": "<PERSON><PERSON> tidak ada penerima yang diten<PERSON>, pesan voting hanya akan dikirim ke grup saat ini.", "no_vote": "Tidak ada voting yang tersedia!", "total_vote": "total suara : *{0}*", "delete_vote": "<PERSON><PERSON> voting saat ini untuk mengatur yang baru.", "option_required": "dua atau lebih opsi diperlukan", "question_required": "<PERSON><PERSON><PERSON> dip<PERSON>lukan", "vote": "<PERSON><PERSON> re<PERSON>i atau balas opsi untuk voting.", "vote_deleted": "_Voting dihapus._", "voted": "@{0} memilih {1}\n\n${2}"}, "warn": {"usage": "Contoh: warn @user ATAU warn reset @user. Anda juga bisa membalas pesan pengguna dan ketik 'warn' atau 'warn reset'.", "desc": "Peringatkan pengguna di grup chat. <PERSON><PERSON><PERSON> ini meningkatkan jumlah peringatan pengguna. Jika jumlahnya melebihi batas, pengguna akan dikeluarkan dari grup. Gunakan 'warn reset' untuk menghapus peringatan pengguna.", "reset_usage": "Contoh: warn reset @user (atau balas ke pesan pengguna dan ketik 'warn reset').", "cannot_remove_admin": "<PERSON>a tidak bisa menghapus admin."}, "wcg": {"usage": "Contoh:\n- wcg start (mulai paksa game rantai kata)\n- wcg end (akhiri game saat ini)\n- wcg <kata> (main dengan melanjutkan rantai kata)", "desc": "Game Rantai Kata: Berpartisipasi dengan memberikan kata yang melanjutkan rantai. Gunakan 'wcg start' untuk memulai game baru atau 'wcg end' untuk menghentikan game saat ini."}, "wrg": {"usage": "Contoh:\n- wrg start (mulai paksa game kata acak)\n- wrg end (akhiri game saat ini)\n- wrg <kata> (main dengan mengirimkan kata)", "desc": "Game Kata Acak: Ikuti game di mana Anda mengirimkan kata sebagai respons terhadap prompt acak. Gunakan 'wrg start' untuk memulai game baru atau 'wrg end' untuk mengakhiri game."}, "yts": {"usage": "Contoh: yts baymax", "desc": "Cari video YouTube berdasarkan kueri atau URL. Jika URL YouTube yang valid diberikan, informasi detail untuk video pertama akan dikembalikan."}, "song": {"usage": "Contoh: song indila love story ATAU song <URL YouTube> (balas ke pesan juga didukung)", "desc": "Unduh lagu dari YouTube. Jika URL diberikan, lagu akan diunduh langsung; ji<PERSON> tidak, pencarian akan dilakukan dan daftar hasil akan dibuat untuk dipilih.", "not_found": "Tidak ada lagu yang ditemukan untuk kueri atau URL yang diberikan."}, "video": {"usage": "Contoh: video <URL YouTube> (atau balas ke pesan dengan URL)", "desc": "Unduh video YouTube. Jika URL langsung diberikan, video akan diunduh; jika kueri pencar<PERSON> diber<PERSON>n, daftar hasil akan dibuat untuk dipilih.", "not_found": "Tidak ada video yang ditemukan untuk kueri atau URL yang diberikan."}, "update": {"usage": "Contoh: update", "desc": "Periksa pembaruan baru. <PERSON><PERSON><PERSON>an pembaruan yang tersedia atau konfirmasi bahwa bot sudah diperbarui.", "up_to_date": "<PERSON>t sudah diperbarui.", "available": "{0} pembaruan baru tersedia:\n{1}"}, "update_now": {"usage": "Contoh: update now", "desc": "<PERSON><PERSON><PERSON> bot ke versi terbaru.", "up_to_date": "Bot sudah diperbarui. Tidak ada pembaruan yang tersedia.", "updating": "<PERSON><PERSON><PERSON><PERSON> bot...", "updated": "Bot berhasil diperbarui!"}, "antigm": {"usage": "> Usage:\n- antigm <on|off> — Enable or disable anti group mention\n- antigm <delete|warn|kick> — Set action when someone mentions the group\n- antigm ignore <jid,jid,jid,...> — Ignore anti group mention from specific groups", "action": "Action updated: group mentions will now result in *{0}*.", "filter": "Ignore JIDs updated for anti group mention.", "enabled": "Anti group mention has been enabled.", "disabled": "Anti group mention has been disabled."}}}