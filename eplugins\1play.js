/**
 * 🎵 YT1Z.net YouTube Downloader Plugin
 *
 * This plugin uses yt1z.net service for YouTube downloads
 * Commands: .yt1z <url/search> - Download using yt1z.net
 */

const { bot, yts, getBuffer, isUrl } = require('../lib/')
const axios = require('axios')

// YouTube URL regex
const ytIdRegex = /(?:http(?:s|):\/\/|)(?:(?:www\.|)youtube(?:\-nocookie|)\.com\/(?:watch\?.*(?:|\&)v=|embed|shorts\/|v\/)|youtu\.be\/)([-_0-9A-Za-z]{11})/

// Safe text formatting
const safeFormat = (text) => {
  if (!text) return 'Unknown'
  return String(text).replace(/[*_`~]/g, '').substring(0, 100)
}

// YT1Z.net API functions
class YT1ZDownloader {
  constructor() {
    this.baseUrl = 'https://yt1z.net'
    this.apiUrl = 'https://yt1z.net/api/button/mp3'
  }

  // Extract video ID from URL
  extractVideoId(url) {
    const match = ytIdRegex.exec(url)
    return match ? match[1] : null
  }

  // Get download link from yt1z.net
  async getDownloadLink(videoId, format = 'mp3') {
    try {
      const videoUrl = `https://www.youtube.com/watch?v=${videoId}`

      // Step 1: Get the conversion page
      const response = await axios.post(this.apiUrl, {
        url: videoUrl,
        format: format,
        quality: format === 'mp3' ? '128' : '720'
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Referer': 'https://yt1z.net/',
          'Origin': 'https://yt1z.net'
        }
      })

      if (response.data && response.data.downloadUrl) {
        return response.data.downloadUrl
      }

      // Alternative method - scrape the page
      return await this.scrapeDownloadLink(videoUrl, format)

    } catch (error) {
      console.error('YT1Z API Error:', error.message)
      throw new Error(`YT1Z service failed: ${error.message}`)
    }
  }

  // Fallback scraping method
  async scrapeDownloadLink(videoUrl, format) {
    try {
      // This is a simplified version - you might need to adjust based on yt1z.net's actual structure
      const formData = new URLSearchParams()
      formData.append('url', videoUrl)
      formData.append('format', format)

      const response = await axios.post('https://yt1z.net/convert', formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })

      // Parse the response to extract download link
      const html = response.data
      const downloadMatch = html.match(/href="([^"]*download[^"]*)"/)

      if (downloadMatch && downloadMatch[1]) {
        return downloadMatch[1].startsWith('http') ? downloadMatch[1] : `https://yt1z.net${downloadMatch[1]}`
      }

      throw new Error('Download link not found in response')

    } catch (error) {
      throw new Error(`Scraping failed: ${error.message}`)
    }
  }
}

// Main plugin command
bot(
  {
    pattern: 'yt1z ?(.*)',
    fromMe: true,
    desc: 'Download YouTube audio using yt1z.net service',
    type: 'download',
  },
  async (message, match) => {
    try {
      match = match || message.reply_message?.text
      if (!match) {
        return await message.send(`*🎵 YT1Z.net YouTube Downloader*

*📝 Usage:*
• \`.yt1z <YouTube URL>\` - Download audio from URL
• \`.yt1z <search term>\` - Search and download audio
• \`.yt1z <search term> auto\` - Auto download first result

*📝 Examples:*
\`.yt1z https://youtu.be/dQw4w9WgXcQ\`
\`.yt1z never gonna give you up auto\`
\`.yt1z despacito\`

*🌐 Powered by yt1z.net*`)
      }

      const downloader = new YT1ZDownloader()
      const vid = ytIdRegex.exec(match)
      const isAutoDownload = match.toLowerCase().includes(' auto')
      const searchTerm = match.replace(/ auto$/i, '').trim()

      // If not a direct URL, search first
      if (!vid) {
        await message.send('🔍 *Searching YouTube...*')
        const result = await yts(searchTerm, false, null, message.id)

        if (!result || !result.length) {
          return await message.send(`❌ *No results found for:* ${searchTerm}`)
        }

        if (isAutoDownload) {
          // Auto download first result
          const topResult = result[0]
          await message.send(`🎵 *Auto-downloading via YT1Z:* ${safeFormat(topResult.title)}`)
          return await downloadWithYT1Z(message, topResult.id, topResult, downloader)
        } else {
          // Show search results for selection
          const { generateList } = require('../lib/')
          const msg = generateList(
            result.slice(0, 8).map(({ title, id, duration, view, author }) => ({
              text: `🎵 ${safeFormat(title)}\n⏱️ ${duration || 'Unknown'} | 👁️ ${view || 'Unknown'}\n👤 ${safeFormat(author)}\n`,
              id: `yt1z https://www.youtube.com/watch?v=${id}`,
            })),
            `🔍 *YT1Z Search Results for:* ${searchTerm}\n\nSelect audio to download:`,
            message.jid,
            message.participant,
            message.id
          )
          return await message.send(msg.message, { quoted: message.data }, msg.type)
        }
      }

      // Direct URL download
      const videoId = vid[1]

      await message.send('📋 *Getting video info...*')
      const [videoInfo] = await yts(videoId, true, null, message.id)
      return await downloadWithYT1Z(message, videoId, videoInfo, downloader)

    } catch (error) {
      console.error('YT1Z Plugin Error:', error)
      return await message.send(`❌ *YT1Z Plugin Error:* ${error.message}`)
    }
  }
)

// Download function using YT1Z
async function downloadWithYT1Z(message, videoId, videoInfo, downloader) {
  try {
    const safeTitle = safeFormat(videoInfo.title)
    const safeAuthor = safeFormat(videoInfo.author)
    const safeDuration = videoInfo.duration || 'Unknown'

    await message.send(`🎵 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n\n🌐 *Processing via YT1Z.net...*`)

    // Get download link from YT1Z
    const downloadUrl = await downloader.getDownloadLink(videoId, 'mp3')

    if (!isUrl(downloadUrl)) {
      throw new Error('Invalid download URL received from YT1Z')
    }

    await message.send('✅ *YT1Z processing complete! Sending audio...*')

    return await message.sendFromUrl(downloadUrl, {
      quoted: message.data,
      mimetype: 'audio/mpeg',
      fileName: `${safeTitle}.mp3`
    })

  } catch (error) {
    console.error('YT1Z Download Error:', error)
    return await message.send(`❌ *YT1Z download failed:* ${error.message}\n\n💡 *Try:*\n• Different video\n• Use regular \`.play\` command\n• Check if yt1z.net is accessible`)
  }
}
