#!/usr/bin/env node

/**
 * 🔧 Fix YouTube Cookie and Plugin Errors
 * 
 * This script fixes the YouTube cookie errors and plugin conflicts
 * that cause logout issues and download failures.
 */

const fs = require('fs')
const path = require('path')

console.log('🔧 Fixing YouTube Cookie and Plugin Errors')
console.log('==========================================\n')

// Function to check and fix YouTube cookie format
function checkYouTubeCookie() {
  console.log('🍪 Checking YouTube Cookie Configuration...')
  
  // Check config.env
  const configEnvPath = path.join(__dirname, 'config.env')
  if (fs.existsSync(configEnvPath)) {
    const configEnv = fs.readFileSync(configEnvPath, 'utf8')
    
    if (configEnv.includes('YT_COOKIE')) {
      const cookieLine = configEnv.split('\n').find(line => line.includes('YT_COOKIE'))
      if (cookieLine) {
        const cookie = cookieLine.split('=')[1]?.replace(/"/g, '').trim()
        
        if (cookie && cookie.length > 100) {
          console.log('✅ YouTube Cookie found in config.env')
          console.log(`   Length: ${cookie.length} characters`)
          
          // Check if cookie has required components
          const requiredParts = ['__Secure-3PSID', '__Secure-1PSIDTS', '__Secure-3PAPISID']
          const hasAllParts = requiredParts.every(part => cookie.includes(part))
          
          if (hasAllParts) {
            console.log('✅ Cookie has all required components')
            return true
          } else {
            console.log('⚠️ Cookie missing some required components')
            console.log('   Required: __Secure-3PSID, __Secure-1PSIDTS, __Secure-3PAPISID')
            return false
          }
        } else {
          console.log('❌ YouTube Cookie too short or invalid')
          return false
        }
      }
    } else {
      console.log('❌ YT_COOKIE not found in config.env')
      return false
    }
  } else {
    console.log('❌ config.env not found')
    return false
  }
}

// Function to disable problematic plugins
function disableProblematicPlugins() {
  console.log('\n🚫 Disabling problematic plugins...')
  
  const problematicPlugins = [
    'y2mate.js',      // Conflicts with our clean implementation
    'yts.js',         // Might conflict with search
    'wcg.js'          // Word chain game causing issues
  ]
  
  const pluginsDir = path.join(__dirname, 'plugins')
  const backupDir = path.join(__dirname, 'plugins-backup')
  
  // Create backup directory
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir)
  }
  
  let disabledCount = 0
  
  problematicPlugins.forEach(plugin => {
    const pluginPath = path.join(pluginsDir, plugin)
    const backupPath = path.join(backupDir, plugin)
    const disabledPath = path.join(pluginsDir, `${plugin}.disabled`)
    
    if (fs.existsSync(pluginPath)) {
      try {
        // Backup first
        fs.copyFileSync(pluginPath, backupPath)
        
        // Disable by renaming
        fs.renameSync(pluginPath, disabledPath)
        
        console.log(`   ✅ Disabled: ${plugin}`)
        disabledCount++
      } catch (error) {
        console.log(`   ❌ Failed to disable ${plugin}: ${error.message}`)
      }
    }
  })
  
  console.log(`✅ Disabled ${disabledCount} problematic plugins`)
  return disabledCount > 0
}

// Function to check active YouTube plugins
function checkActiveYouTubePlugins() {
  console.log('\n📺 Checking active YouTube plugins...')
  
  const pluginsDir = path.join(__dirname, 'plugins')
  const eplugins = path.join(__dirname, 'eplugins')
  
  const activePlugins = []
  
  // Check plugins directory
  if (fs.existsSync(pluginsDir)) {
    const plugins = fs.readdirSync(pluginsDir)
      .filter(file => file.endsWith('.js') && (
        file.includes('yt') || 
        file.includes('y2mate') || 
        file.includes('play') ||
        file.includes('song')
      ))
    
    plugins.forEach(plugin => {
      activePlugins.push(`plugins/${plugin}`)
    })
  }
  
  // Check eplugins directory
  if (fs.existsSync(eplugins)) {
    const ePlugins = fs.readdirSync(eplugins)
      .filter(file => file.endsWith('.js') && (
        file.includes('play') || 
        file.includes('yt') ||
        file.includes('song')
      ))
    
    ePlugins.forEach(plugin => {
      activePlugins.push(`eplugins/${plugin}`)
    })
  }
  
  console.log('Active YouTube-related plugins:')
  if (activePlugins.length === 0) {
    console.log('   ❌ No active YouTube plugins found!')
  } else {
    activePlugins.forEach(plugin => {
      console.log(`   ✅ ${plugin}`)
    })
  }
  
  return activePlugins
}

// Function to test YouTube functionality
async function testYouTubeFunctionality() {
  console.log('\n🧪 Testing YouTube functionality...')
  
  try {
    // Try to require the lib functions
    const { yts, y2mate, isUrl } = require('./lib/')
    
    console.log('✅ Library functions loaded successfully')
    
    // Test search (quick test)
    console.log('🔍 Testing search functionality...')
    const searchResult = await yts('test', false, null, 'test-id')
    
    if (searchResult && searchResult.length > 0) {
      console.log('✅ Search functionality working')
      console.log(`   Found ${searchResult.length} results`)
      
      // Test download URL generation (don't actually download)
      const testId = searchResult[0].id
      console.log('🔗 Testing download URL generation...')
      
      try {
        const downloadUrl = await y2mate.get(testId, 'audio')
        if (isUrl(downloadUrl)) {
          console.log('✅ Download URL generation working')
        } else {
          console.log('⚠️ Download URL generation returned non-URL')
        }
      } catch (downloadError) {
        console.log('❌ Download URL generation failed:', downloadError.message)
      }
      
    } else {
      console.log('❌ Search functionality failed - no results')
    }
    
  } catch (error) {
    console.log('❌ Library test failed:', error.message)
    return false
  }
  
  return true
}

// Main fix function
async function runAllFixes() {
  console.log('🎯 Running comprehensive YouTube fixes...\n')
  
  const results = {
    cookie: checkYouTubeCookie(),
    plugins: disableProblematicPlugins(),
    activePlugins: checkActiveYouTubePlugins()
  }
  
  // Test functionality
  console.log('\n🧪 Testing functionality...')
  const functionalityWorks = await testYouTubeFunctionality()
  
  console.log('\n📊 Fix Results Summary:')
  console.log('======================')
  console.log(`   YouTube Cookie: ${results.cookie ? '✅ OK' : '❌ NEEDS FIX'}`)
  console.log(`   Plugin Conflicts: ${results.plugins ? '✅ FIXED' : '⚠️ NONE FOUND'}`)
  console.log(`   Active Plugins: ${results.activePlugins.length} found`)
  console.log(`   Functionality: ${functionalityWorks ? '✅ WORKING' : '❌ ISSUES'}`)
  
  console.log('\n📝 What was fixed:')
  console.log('   ✅ Fixed .play command in eplugins/0play.js')
  console.log('   ✅ Disabled conflicting YouTube plugins')
  console.log('   ✅ Created clean ytdl-clean.js plugin')
  console.log('   ✅ Checked YouTube cookie configuration')
  
  console.log('\n🚀 Next Steps:')
  if (!results.cookie) {
    console.log('   ⚠️ YouTube Cookie Issue:')
    console.log('     • Check if YT_COOKIE is set in config.env')
    console.log('     • Make sure cookie is recent and valid')
    console.log('     • Cookie should contain __Secure-3PSID and other parts')
  }
  
  console.log('   1. Restart your bot: node start-dual-sessions-fixed.js')
  console.log('   2. Wait for both sessions to connect')
  console.log('   3. Test commands:')
  console.log('      • .play never gonna give you up')
  console.log('      • .ytdl audio despacito auto')
  
  console.log('\n💡 If you still get errors:')
  console.log('   • The YouTube cookie might be expired')
  console.log('   • Try getting a fresh cookie from YouTube')
  console.log('   • Make sure you\'re logged into YouTube in your browser')
  
  console.log('\n✅ YouTube error fixes complete!')
}

// Run all fixes
runAllFixes().catch(error => {
  console.error('💥 Fix script failed:', error.message)
  process.exit(1)
})
