const { bot, textMaker } = require('../lib')

bot(
	{
		pattern: 'adglow ?(.*)',
		fromMe: true,
		desc: 'Advanced glow effects',
		type: 'textmaker',
	},
	async (message, match) => {
		if (!match) return await message.send(' _Give me text_ \n 💠 *Example* - *.adglow* *Prabhath*')
		const effect_url =
			'https://en.ephoto360.com/advanced-glow-effects-74.html'
		const { status, url } = await textMaker(effect_url, match)
		if (url) return await message.sendFromUrl(url)
	}
)