#!/usr/bin/env node

const { spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

// Load session configurations
const configPath = path.join(__dirname, 'config.json')
let sessionConfigs = {}

if (fs.existsSync(configPath)) {
  try {
    sessionConfigs = JSON.parse(fs.readFileSync(configPath, 'utf8'))
  } catch (error) {
    console.error('Failed to load config.json:', error.message)
    process.exit(1)
  }
} else {
  console.error('config.json not found. Please create it with your session configurations.')
  process.exit(1)
}

const runningProcesses = []

const startSession = (sessionName, sessionConfig) => {
  console.log(`🚀 Starting session: ${sessionName}`)

  if (!sessionConfig.SESSION_ID || sessionConfig.SESSION_ID === 'levanter_sessionid') {
    console.warn(`⚠️  Skipping ${sessionName}: Invalid or placeholder SESSION_ID`)
    return null
  }

  // Create environment variables for this session
  const env = { ...process.env, ...sessionConfig }

  // Add unique database path for each session to avoid conflicts
  const dbPath = path.join(__dirname, `database_${sessionName}.db`)
  env.DATABASE_URL = dbPath

  // Start the session as a separate process
  const child = spawn('node', ['index.js'], {
    env,
    stdio: ['inherit', 'pipe', 'pipe'],
    cwd: __dirname
  })

  // Handle output
  child.stdout.on('data', (data) => {
    console.log(`[${sessionName}] ${data.toString().trim()}`)
  })

  child.stderr.on('data', (data) => {
    console.error(`[${sessionName}] ERROR: ${data.toString().trim()}`)
  })

  child.on('close', (code) => {
    console.log(`[${sessionName}] Process exited with code ${code}`)
    // Remove from running processes
    const index = runningProcesses.findIndex(p => p.name === sessionName)
    if (index > -1) {
      runningProcesses.splice(index, 1)
    }
  })

  child.on('error', (error) => {
    console.error(`[${sessionName}] Failed to start: ${error.message}`)
  })

  runningProcesses.push({ name: sessionName, process: child })
  return child
}

const start = () => {
  console.log('🤖 Levanter Multi-Session Manager')
  console.log('================================')

  const sessionNames = Object.keys(sessionConfigs)

  if (sessionNames.length === 0) {
    console.error('❌ No sessions configured in config.json')
    process.exit(1)
  }

  console.log(`📋 Found ${sessionNames.length} session(s): ${sessionNames.join(', ')}`)
  console.log('')

  // Start all sessions with delays
  sessionNames.forEach((sessionName, index) => {
    setTimeout(() => {
      const sessionConfig = sessionConfigs[sessionName]
      startSession(sessionName, sessionConfig)
    }, index * 3000) // 3 second delay between each session
  })

  // Handle graceful shutdown
  const shutdown = (signal) => {
    console.log(`\n🛑 Received ${signal}, shutting down all sessions...`)
    runningProcesses.forEach(({ name, process }) => {
      console.log(`   Stopping ${name}...`)
      process.kill('SIGTERM')
    })

    // Force exit after 10 seconds
    setTimeout(() => {
      console.log('🔴 Force exiting...')
      process.exit(0)
    }, 10000)
  }

  process.on('SIGINT', () => shutdown('SIGINT'))
  process.on('SIGTERM', () => shutdown('SIGTERM'))
}

start()
