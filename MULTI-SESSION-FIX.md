# Multi-Session Bot Logout Fix

## Problem
When using 2 WhatsApp sessions, after bot restart, using a command on the first session causes the bot to automatically log out of the linked device.

## Why This Happens
1. **WhatsApp Security**: WhatsApp Web limits simultaneous sessions
2. **Session Conflict**: Both sessions try to reconnect at the same time after restart
3. **Device Limit**: WhatsApp sees multiple sessions as suspicious activity

## Solutions

### Solution 1: Session Startup Delay
Add a delay between session startups to avoid conflicts.

**Create `session-manager.js`:**
```javascript
const { spawn } = require('child_process')

function startSession(sessionId, delay = 0) {
  setTimeout(() => {
    console.log(`Starting session ${sessionId}...`)
    
    const env = { ...process.env, SESSION_ID: sessionId }
    const child = spawn('node', ['index.js'], { 
      env,
      stdio: 'inherit',
      cwd: process.cwd()
    })
    
    child.on('exit', (code) => {
      console.log(`Session ${sessionId} exited with code ${code}`)
      // Auto-restart after 10 seconds
      setTimeout(() => startSession(sessionId, 5000), 10000)
    })
    
  }, delay)
}

// Start sessions with delay
startSession('session1', 0)      // Start immediately
startSession('session2', 30000)  // Start after 30 seconds
```

### Solution 2: Separate Auth Directories
Ensure each session uses different authentication storage.

**In your main bot file:**
```javascript
const sessionId = process.env.SESSION_ID || 'session1'
const authPath = `./auth/${sessionId}`

// Use different auth paths for each session
const config = {
  sessionId: sessionId,
  authPath: authPath,
  // ... other config
}
```

### Solution 3: Environment-Based Configuration
Create separate environment files for each session.

**`.env.session1`:**
```
SESSION_ID=session1
AUTH_PATH=./auth/session1
PORT=3001
```

**`.env.session2`:**
```
SESSION_ID=session2
AUTH_PATH=./auth/session2
PORT=3002
```

**Start commands:**
```bash
# Terminal 1
node --env-file=.env.session1 index.js

# Terminal 2 (wait 30 seconds)
node --env-file=.env.session2 index.js
```

### Solution 4: Docker Containers (Advanced)
Run each session in separate Docker containers.

**docker-compose.yml:**
```yaml
version: '3.8'
services:
  bot-session1:
    build: .
    environment:
      - SESSION_ID=session1
    volumes:
      - ./auth/session1:/app/auth
    restart: unless-stopped
    
  bot-session2:
    build: .
    environment:
      - SESSION_ID=session2
    volumes:
      - ./auth/session2:/app/auth
    restart: unless-stopped
    depends_on:
      - bot-session1
```

## Recommended Quick Fix

**Step 1:** Create session startup script
```javascript
// session-start.js
const { exec } = require('child_process')

console.log('Starting Session 1...')
const session1 = exec('node index.js', { env: { ...process.env, SESSION_ID: 'session1' } })

setTimeout(() => {
  console.log('Starting Session 2...')
  const session2 = exec('node index.js', { env: { ...process.env, SESSION_ID: 'session2' } })
}, 30000) // 30 second delay
```

**Step 2:** Modify your main bot file to handle session IDs
```javascript
const sessionId = process.env.SESSION_ID || 'default'
console.log(`Starting bot with session: ${sessionId}`)

// Use different auth paths
const authPath = `./auth/${sessionId}`
```

**Step 3:** Start with the script
```bash
node session-start.js
```

## Additional Tips

1. **Monitor Sessions**: Add logging to track which session is active
2. **Graceful Shutdown**: Handle SIGTERM to close sessions properly
3. **Health Checks**: Ping each session periodically to ensure they're alive
4. **Backup Auth**: Keep backups of auth folders for each session

## For YouTube Downloads

Since external APIs are blocked, use the new local commands:
- `.ytlocal <url>` - Local video download using only y2mate library
- `.ytaudio <url>` - Local audio download using only y2mate library

These commands bypass external APIs and use only your existing y2mate library.

## Testing

1. Start both sessions with delay
2. Wait for both to connect
3. Test commands on session 1
4. Check if session 2 stays connected
5. If logout still occurs, increase the startup delay to 60 seconds
