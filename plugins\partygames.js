const { bot } = require('../lib/')

// Enhanced Party Games with achievements, stats, and interactive features
const partyGameStats = new Map()
const activePartyGames = new Map()
const partyAchievements = new Map()
const gameHistory = new Map()

// Party game achievements
const PARTY_ACHIEVEMENTS = {
  'truth_seeker': { name: '🔍 Truth Seeker', desc: 'Answer 10 truth questions', points: 20 },
  'dare_devil': { name: '😈 Dare Devil', desc: 'Complete 10 dares', points: 25 },
  'puzzle_master': { name: '🧩 Puzzle Master', desc: 'Solve 15 emoji puzzles', points: 30 },
  'math_genius': { name: '🧮 Math Genius', desc: 'Solve 20 math problems', points: 35 },
  'party_starter': { name: '🎉 Party Starter', desc: 'Start 5 different party games', points: 15 },
  'social_king': { name: '👑 Social King', desc: 'Play party games with 10+ people', points: 40 },
  'speed_solver': { name: '⚡ Speed Solver', desc: 'Solve puzzle in under 10 seconds', points: 25 },
  'creative_mind': { name: '🎨 Creative Mind', desc: 'Complete 5 creative dares', points: 20 }
}

// Helper functions for party games
function updatePartyStats(playerId, gameType, result, points = 0) {
  if (!partyGameStats.has(playerId)) {
    partyGameStats.set(playerId, {
      truthsAnswered: 0,
      daresCompleted: 0,
      puzzlesSolved: 0,
      mathSolved: 0,
      gamesStarted: 0,
      totalPoints: 0,
      achievements: [],
      favoritePartyGame: null,
      lastPlayed: Date.now()
    })
  }

  const stats = partyGameStats.get(playerId)
  stats.lastPlayed = Date.now()
  stats.totalPoints += points

  // Update specific game stats
  if (gameType === 'truth') stats.truthsAnswered++
  if (gameType === 'dare') stats.daresCompleted++
  if (gameType === 'puzzle') stats.puzzlesSolved++
  if (gameType === 'math') stats.mathSolved++
  if (result === 'started') stats.gamesStarted++

  partyGameStats.set(playerId, stats)
  checkPartyAchievements(playerId, gameType, result)
}

function checkPartyAchievements(playerId, gameType, result) {
  const stats = partyGameStats.get(playerId)
  const playerAchievements = partyAchievements.get(playerId) || []

  // Check achievements
  if (stats.truthsAnswered >= 10 && !playerAchievements.includes('truth_seeker')) {
    playerAchievements.push('truth_seeker')
    stats.totalPoints += PARTY_ACHIEVEMENTS.truth_seeker.points
  }

  if (stats.daresCompleted >= 10 && !playerAchievements.includes('dare_devil')) {
    playerAchievements.push('dare_devil')
    stats.totalPoints += PARTY_ACHIEVEMENTS.dare_devil.points
  }

  if (stats.puzzlesSolved >= 15 && !playerAchievements.includes('puzzle_master')) {
    playerAchievements.push('puzzle_master')
    stats.totalPoints += PARTY_ACHIEVEMENTS.puzzle_master.points
  }

  if (stats.mathSolved >= 20 && !playerAchievements.includes('math_genius')) {
    playerAchievements.push('math_genius')
    stats.totalPoints += PARTY_ACHIEVEMENTS.math_genius.points
  }

  if (stats.gamesStarted >= 5 && !playerAchievements.includes('party_starter')) {
    playerAchievements.push('party_starter')
    stats.totalPoints += PARTY_ACHIEVEMENTS.party_starter.points
  }

  partyAchievements.set(playerId, playerAchievements)
  partyGameStats.set(playerId, stats)
}

// Enhanced Truth or Dare Game
bot(
  {
    pattern: 'tod ?(truth|dare|stats|help|leaderboard)?',
    desc: 'Enhanced Truth or Dare with achievements and stats',
    type: 'game',
  },
  async (message, match) => {
    const player = message.participant || message.sender

    if (match === 'help') {
      return await message.send(
        `🎭 *TRUTH OR DARE GUIDE*\n\n` +
        `🎯 *Commands:*\n` +
        `• \`.tod\` - Random truth or dare\n` +
        `• \`.tod truth\` - Get a truth question\n` +
        `• \`.tod dare\` - Get a dare challenge\n` +
        `• \`.tod stats\` - View your stats\n` +
        `• \`.tod leaderboard\` - Top players\n\n` +
        `🏆 *Achievements:*\n` +
        `• Truth Seeker (10 truths)\n` +
        `• Dare Devil (10 dares)\n` +
        `• Creative Mind (5 creative dares)\n\n` +
        `💡 *Tips:*\n` +
        `• Be honest with truths!\n` +
        `• Complete dares for points!\n` +
        `• Share proof of dares for bonus points!`
      )
    }

    if (match === 'stats') {
      const stats = partyGameStats.get(player)
      if (!stats) {
        return await message.send(`📊 *No Truth or Dare stats yet!* Play some games first with \`.tod\``)
      }

      const playerAchievements = partyAchievements.get(player) || []

      return await message.send(
        `🎭 *Your Truth or Dare Stats*\n\n` +
        `🤔 *Truths Answered:* ${stats.truthsAnswered}\n` +
        `😈 *Dares Completed:* ${stats.daresCompleted}\n` +
        `⭐ *Total Points:* ${stats.totalPoints}\n` +
        `🎮 *Games Started:* ${stats.gamesStarted}\n\n` +
        `🏅 *Achievements (${playerAchievements.length}/${Object.keys(PARTY_ACHIEVEMENTS).length}):*\n` +
        `${playerAchievements.map(a => PARTY_ACHIEVEMENTS[a]?.name || a).join('\n') || 'None yet - keep playing!'}`,
        { mentions: [player] }
      )
    }

    if (match === 'leaderboard') {
      const allStats = Array.from(partyGameStats.entries())
        .sort(([,a], [,b]) => b.totalPoints - a.totalPoints)
        .slice(0, 10)

      if (allStats.length === 0) {
        return await message.send(`🏆 *No leaderboard data yet!* Start playing with \`.tod\``)
      }

      const leaderboard = allStats.map(([playerId, stats], index) => {
        const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '🏅'
        return `${medal} @${playerId.split('@')[0]}: ${stats.totalPoints} pts (${stats.truthsAnswered}T/${stats.daresCompleted}D)`
      }).join('\n')

      return await message.send(
        `🏆 *TRUTH OR DARE LEADERBOARD*\n\n${leaderboard}`,
        { mentions: allStats.map(([playerId]) => playerId) }
      )
    }

    const truths = [
      "What's the most embarrassing thing you've ever done?",
      "Who was your first crush and why?",
      "What's your biggest fear that you've never told anyone?",
      "What's the weirdest dream you've ever had?",
      "If you could date anyone in this group, who would it be and why?",
      "What's your most embarrassing childhood memory?",
      "What's the biggest lie you've ever told your parents?",
      "Who in this group would you trust with your biggest secret?",
      "What's something you've never told your best friend?",
      "What's your guilty pleasure that you're ashamed of?",
      "If you had to delete one app from your phone forever, what would it be?",
      "What's the most childish thing you still do regularly?",
      "Who was the last person you stalked on social media for hours?",
      "What's your biggest regret in life so far?",
      "If you could read minds for a day, whose would you read first?",
      "What's the most embarrassing thing in your search history?",
      "What's a secret you've never told anyone in this group?",
      "What's the worst thing you've done that your parents don't know about?",
      "Who do you have a secret crush on right now?",
      "What's the most embarrassing thing you've done in front of your crush?"
    ]
    
    const dares = [
      "Send a funny selfie to the group with a weird face",
      "Do your best impression of someone in this group (tag them!)",
      "Sing the chorus of your favorite song and send voice note",
      "Do 10 push-ups and send a video as proof",
      "Text your crush (or ex) 'Hey, thinking of you' and screenshot it",
      "Post an embarrassing childhood photo on your story for 1 hour",
      "Do a silly dance and send a video (minimum 30 seconds)",
      "Call a random contact and sing happy birthday to them",
      "Speak in a funny accent for the next 10 minutes in this chat",
      "Let someone else write your WhatsApp status for a day",
      "Do your best animal impression and send voice note",
      "Send a voice note singing your favorite song (full chorus)",
      "Do a handstand for 30 seconds and send video proof",
      "Text your mom/dad 'I love you' right now and screenshot",
      "Make up a 30-second rap about someone in this group",
      "Send a video of you doing your weirdest talent",
      "Change your profile picture to something embarrassing for 2 hours",
      "Send a voice note telling a joke in a serious tone",
      "Do 20 jumping jacks and send video proof",
      "Send a selfie with 5 random objects from your room",
      "Record yourself saying 'I'm a little teapot' song with actions",
      "Send a video of you trying to lick your elbow",
      "Text 'I miss you' to the 5th person in your contacts",
      "Send a photo of the weirdest thing in your fridge",
      "Do your best celebrity impression and send voice note"
    ]
    
    if (match === 'truth') {
      const randomTruth = truths[Math.floor(Math.random() * truths.length)]
      updatePartyStats(player, 'truth', 'received', 5)

      return await message.send(
        `🤔 *TRUTH for @${player.split('@')[0]}*\n\n` +
        `❓ ${randomTruth}\n\n` +
        `⏰ *You must answer honestly!*\n` +
        `💰 *+5 points for answering*\n\n` +
        `💡 *Reply with your answer to get points!*`,
        { mentions: [player] }
      )
    }

    if (match === 'dare') {
      const randomDare = dares[Math.floor(Math.random() * dares.length)]
      updatePartyStats(player, 'dare', 'received', 10)

      const isCreativeDare = randomDare.includes('video') || randomDare.includes('voice') || randomDare.includes('photo')
      const points = isCreativeDare ? 15 : 10

      return await message.send(
        `😈 *DARE for @${player.split('@')[0]}*\n\n` +
        `🎯 ${randomDare}\n\n` +
        `⚡ *You must complete this dare!*\n` +
        `💰 *+${points} points for completing*\n` +
        `${isCreativeDare ? '🎨 *CREATIVE DARE - Bonus points!*\n' : ''}` +
        `📸 *Send proof to get points!*`,
        { mentions: [player] }
      )
    }
    
    // Random choice with enhanced features
    const isTruth = Math.random() < 0.5
    const randomQuestion = isTruth ?
      truths[Math.floor(Math.random() * truths.length)] :
      dares[Math.floor(Math.random() * dares.length)]

    updatePartyStats(player, isTruth ? 'truth' : 'dare', 'received', isTruth ? 5 : 10)

    const isCreativeDare = !isTruth && (randomQuestion.includes('video') || randomQuestion.includes('voice') || randomQuestion.includes('photo'))
    const points = isTruth ? 5 : (isCreativeDare ? 15 : 10)

    return await message.send(
      `🎲 *Truth or Dare for @${player.split('@')[0]}*\n\n` +
      `${isTruth ? '🤔 *TRUTH:*' : '😈 *DARE:*'}\n` +
      `${randomQuestion}\n\n` +
      `💰 *+${points} points for ${isTruth ? 'answering' : 'completing'}*\n` +
      `${isCreativeDare ? '🎨 *CREATIVE DARE - Bonus points!*\n' : ''}` +
      `${isTruth ? '💡 *Reply with your honest answer!*' : '📸 *Send proof to get points!*'}\n\n` +
      `🎯 *Next time specify:* \`.tod truth\` or \`.tod dare\``,
      { mentions: [player] }
    )
  }
)

// Enhanced Would You Rather Game
bot(
  {
    pattern: 'wyr ?(random|spicy|funny|deep|stats)?',
    desc: 'Enhanced Would You Rather with categories and voting',
    type: 'game',
  },
  async (message, match) => {
    const player = message.participant || message.sender

    if (match === 'stats') {
      const stats = partyGameStats.get(player)
      if (!stats) {
        return await message.send(`📊 *No Would You Rather stats yet!* Play some games first with \`.wyr\``)
      }

      return await message.send(
        `🤔 *Your Would You Rather Stats*\n\n` +
        `🎮 *Questions answered:* ${stats.wyrAnswered || 0}\n` +
        `⭐ *Points earned:* ${stats.totalPoints}\n` +
        `🏆 *Achievements:* ${(partyAchievements.get(player) || []).length}\n\n` +
        `💡 *Keep playing to unlock more achievements!*`,
        { mentions: [player] }
      )
    }

    const randomQuestions = [
      "Would you rather have the ability to fly OR be invisible?",
      "Would you rather always be 10 minutes late OR 20 minutes early?",
      "Would you rather have unlimited money OR unlimited time?",
      "Would you rather be famous OR be the best friend of someone famous?",
      "Would you rather live without music OR live without movies?",
      "Would you rather always be hot OR always be cold?",
      "Would you rather have super strength OR super speed?",
      "Would you rather have a rewind button OR a pause button for your life?",
      "Would you rather be able to speak all languages OR play all instruments?",
      "Would you rather live in the past OR live in the future?"
    ]

    const spicyQuestions = [
      "Would you rather date someone 10 years older OR 10 years younger?",
      "Would you rather have your browser history made public OR your text messages?",
      "Would you rather kiss your crush in public OR never kiss them at all?",
      "Would you rather have a one night stand with your celebrity crush OR a romantic dinner with your real crush?",
      "Would you rather accidentally send a love text to your boss OR your ex?",
      "Would you rather have your parents see your search history OR your friends see your camera roll?",
      "Would you rather be caught singing in the shower OR talking to yourself in the mirror?",
      "Would you rather have to wear your clothes inside out OR backwards for a week?",
      "Would you rather have everyone know your salary OR your age?",
      "Would you rather have your most embarrassing moment broadcast on TV OR posted on social media?"
    ]

    const funnyQuestions = [
      "Would you rather have fingers as long as legs OR legs as short as fingers?",
      "Would you rather sweat mayo OR cry ketchup?",
      "Would you rather have a head the size of a tennis ball OR the size of a watermelon?",
      "Would you rather have to hop everywhere OR crawl everywhere?",
      "Would you rather have a permanent clown nose OR permanent clown shoes?",
      "Would you rather speak only in rhymes OR sing everything you say?",
      "Would you rather have a tail OR have horns?",
      "Would you rather have to wear a tuxedo every day OR pajamas every day?",
      "Would you rather have spaghetti for hair OR marshmallows for teeth?",
      "Would you rather have to dance every time you hear music OR sing every time you hear your name?"
    ]

    const deepQuestions = [
      "Would you rather know when you're going to die OR how you're going to die?",
      "Would you rather be able to change the past OR see into the future?",
      "Would you rather lose all your memories OR never be able to make new ones?",
      "Would you rather be loved by everyone but feel empty inside OR be hated by everyone but feel fulfilled?",
      "Would you rather have the power to end world hunger OR end all wars?",
      "Would you rather know all the mysteries of the universe OR be blissfully ignorant?",
      "Would you rather sacrifice yourself to save 100 strangers OR save yourself and let them die?",
      "Would you rather live a short life full of adventure OR a long life of routine?",
      "Would you rather be remembered for something terrible OR be completely forgotten?",
      "Would you rather have the ability to change one decision from your past OR guarantee one decision in your future?"
    ]

    let questions, category
    switch (match) {
      case 'spicy':
        questions = spicyQuestions
        category = 'SPICY 🌶️'
        break
      case 'funny':
        questions = funnyQuestions
        category = 'FUNNY 😂'
        break
      case 'deep':
        questions = deepQuestions
        category = 'DEEP 🤯'
        break
      default:
        questions = randomQuestions
        category = 'RANDOM 🎲'
    }

    const randomQuestion = questions[Math.floor(Math.random() * questions.length)]
    updatePartyStats(player, 'wyr', 'started', 3)

    // Store the active question for voting
    activePartyGames.set(message.jid, {
      type: 'wyr',
      question: randomQuestion,
      category: category,
      startTime: Date.now(),
      votes: new Map(),
      starter: player
    })

    return await message.send(
      `🤔 *WOULD YOU RATHER* - ${category}\n\n` +
      `❓ ${randomQuestion}\n\n` +
      `🗳️ *Vote by typing:* \`A\` or \`B\`\n` +
      `💭 *Everyone participate and explain your choice!*\n` +
      `⏱️ *Voting ends in 2 minutes*\n\n` +
      `🎯 *Categories:* random, spicy, funny, deep\n` +
      `📊 *Type* \`.wyr stats\` *for your statistics*`
    )
  }
)

// Would You Rather voting handler
bot(
  {
    pattern: '^[AaBb]$',
    fromMe: false,
    dontAddCommandList: true,
  },
  async (message, match) => {
    const gameId = message.jid
    const player = message.participant || message.sender
    const game = activePartyGames.get(gameId)

    if (!game || game.type !== 'wyr') return

    const vote = match.toUpperCase()

    // Check if already voted
    if (game.votes.has(player)) {
      return await message.send(`🗳️ *You already voted ${game.votes.get(player)}!* Wait for the next question.`)
    }

    game.votes.set(player, vote)
    updatePartyStats(player, 'wyr', 'voted', 2)

    await message.send(
      `✅ *Vote recorded!* @${player.split('@')[0]} chose **${vote}**\n\n` +
      `🗳️ *Total votes:* ${game.votes.size}\n` +
      `💡 *Explain your choice for bonus points!*`,
      { mentions: [player] }
    )

    // Auto-end voting after 2 minutes
    if (Date.now() - game.startTime > 120000) {
      const voteA = Array.from(game.votes.values()).filter(v => v === 'A').length
      const voteB = Array.from(game.votes.values()).filter(v => v === 'B').length

      activePartyGames.delete(gameId)

      return await message.send(
        `📊 *VOTING RESULTS*\n\n` +
        `${game.question}\n\n` +
        `🅰️ *Option A:* ${voteA} votes\n` +
        `🅱️ *Option B:* ${voteB} votes\n\n` +
        `🏆 *Winner:* ${voteA > voteB ? 'Option A' : voteB > voteA ? 'Option B' : 'Tie!'}\n\n` +
        `🎮 *Play again with* \`.wyr\``,
        { quoted: message.data }
      )
    }
  }
)

// Enhanced Never Have I Ever Game
bot(
  {
    pattern: 'nhie ?(random|spicy|funny|stats)?',
    desc: 'Enhanced Never Have I Ever with categories and scoring',
    type: 'game',
  },
  async (message, match) => {
    const player = message.participant || message.sender

    if (match === 'stats') {
      const stats = partyGameStats.get(player)
      if (!stats) {
        return await message.send(`📊 *No Never Have I Ever stats yet!* Play some games first with \`.nhie\``)
      }

      return await message.send(
        `🙈 *Your Never Have I Ever Stats*\n\n` +
        `🎮 *Statements answered:* ${stats.nhieAnswered || 0}\n` +
        `🙋‍♀️ *"I have" responses:* ${stats.nhieHave || 0}\n` +
        `⭐ *Points earned:* ${stats.totalPoints}\n\n` +
        `💡 *Keep playing to unlock achievements!*`,
        { mentions: [player] }
      )
    }

    const randomStatements = [
      "Never have I ever skipped school/work to play video games",
      "Never have I ever pretended to be sick to avoid something",
      "Never have I ever forgotten someone's name right after being introduced",
      "Never have I ever laughed so hard I cried",
      "Never have I ever sent a text to the wrong person",
      "Never have I ever fallen asleep during a movie in theaters",
      "Never have I ever googled myself",
      "Never have I ever pretended to understand something I didn't",
      "Never have I ever eaten food that fell on the floor",
      "Never have I ever sung in the shower",
      "Never have I ever talked to myself in the mirror",
      "Never have I ever stayed up all night binge-watching a series"
    ]

    const spicyStatements = [
      "Never have I ever had a crush on someone in this group",
      "Never have I ever stalked someone on social media for hours",
      "Never have I ever accidentally liked an old photo while stalking someone",
      "Never have I ever had a romantic dream about someone I know",
      "Never have I ever pretended to like someone I actually didn't",
      "Never have I ever kissed someone I regretted kissing",
      "Never have I ever had feelings for my friend's crush",
      "Never have I ever lied about my relationship status",
      "Never have I ever had a secret social media account",
      "Never have I ever read someone else's diary or messages"
    ]

    const funnyStatements = [
      "Never have I ever tried to look cool and failed miserably",
      "Never have I ever walked into a glass door",
      "Never have I ever waved back at someone who wasn't waving at me",
      "Never have I ever pretended to be on the phone to avoid someone",
      "Never have I ever fallen off a chair",
      "Never have I ever laughed at something I didn't understand",
      "Never have I ever gotten lost in a place I've been to many times",
      "Never have I ever tried to push a door that says 'pull'",
      "Never have I ever forgotten what I was saying mid-sentence",
      "Never have I ever tried to be funny and made it awkward instead"
    ]

    let statements, category
    switch (match) {
      case 'spicy':
        statements = spicyStatements
        category = 'SPICY 🌶️'
        break
      case 'funny':
        statements = funnyStatements
        category = 'FUNNY 😂'
        break
      default:
        statements = randomStatements
        category = 'RANDOM 🎲'
    }

    const randomStatement = statements[Math.floor(Math.random() * statements.length)]
    updatePartyStats(player, 'nhie', 'started', 3)

    // Store active game for response tracking
    activePartyGames.set(message.jid, {
      type: 'nhie',
      statement: randomStatement,
      category: category,
      startTime: Date.now(),
      responses: new Map(),
      starter: player
    })

    return await message.send(
      `🙈 *NEVER HAVE I EVER* - ${category}\n\n` +
      `💭 ${randomStatement}\n\n` +
      `🙋‍♀️ *If you HAVE done this, type "I have" or "Yes"*\n` +
      `🙅‍♂️ *If you HAVEN'T, type "Never" or "No"*\n` +
      `💰 *+5 points for honest participation!*\n\n` +
      `🎯 *Categories:* random, spicy, funny\n` +
      `📊 *Type* \`.nhie stats\` *for your statistics*`
    )
  }
)

// Never Have I Ever response handler
bot(
  {
    pattern: '(i have|yes|never|no)',
    fromMe: false,
    dontAddCommandList: true,
  },
  async (message, match) => {
    const gameId = message.jid
    const player = message.participant || message.sender
    const game = activePartyGames.get(gameId)

    if (!game || game.type !== 'nhie') return

    const response = match.toLowerCase()
    const hasResponse = response.includes('have') || response === 'yes'

    // Check if already responded
    if (game.responses.has(player)) {
      return await message.send(`🙈 *You already responded!* Wait for the next statement.`)
    }

    game.responses.set(player, hasResponse)
    updatePartyStats(player, 'nhie', 'answered', 5)

    if (hasResponse) {
      const stats = partyGameStats.get(player)
      stats.nhieHave = (stats.nhieHave || 0) + 1
      partyGameStats.set(player, stats)
    }

    await message.send(
      `✅ *Response recorded!* @${player.split('@')[0]} ${hasResponse ? 'HAS' : 'has NEVER'} done this\n\n` +
      `🙈 *Total responses:* ${game.responses.size}\n` +
      `💰 *+5 points for honesty!*`,
      { mentions: [player] }
    )

    // Show results after 90 seconds or when enough people respond
    if (Date.now() - game.startTime > 90000 || game.responses.size >= 5) {
      const haveCount = Array.from(game.responses.values()).filter(r => r).length
      const neverCount = game.responses.size - haveCount

      activePartyGames.delete(gameId)

      return await message.send(
        `📊 *NEVER HAVE I EVER RESULTS*\n\n` +
        `${game.statement}\n\n` +
        `🙋‍♀️ *Have done it:* ${haveCount} people\n` +
        `🙅‍♂️ *Never done it:* ${neverCount} people\n\n` +
        `${haveCount > neverCount ? '😱 *Most people have done this!*' : '😇 *Most people are innocent!*'}\n\n` +
        `🎮 *Play again with* \`.nhie\``,
        { quoted: message.data }
      )
    }
  }
)

// Enhanced Emoji Puzzle Game
bot(
  {
    pattern: 'emojipuzzle ?(movies|songs|animals|food|brands|countries|stats|help)?',
    desc: 'Enhanced emoji puzzle game with achievements and categories',
    type: 'game',
  },
  async (message, match) => {
    const player = message.participant || message.sender

    if (match === 'help') {
      return await message.send(
        `🧩 *EMOJI PUZZLE GUIDE*\n\n` +
        `🎯 *How to Play:*\n` +
        `• Guess what the emojis represent\n` +
        `• First correct answer wins points\n` +
        `• Use hints if you're stuck\n\n` +
        `🏆 *Scoring:*\n` +
        `• Correct answer: +10 points\n` +
        `• Speed bonus: +5 points (under 30s)\n` +
        `• No hint bonus: +3 points\n\n` +
        `📚 *Categories:*\n` +
        `• movies, songs, animals, food\n` +
        `• brands, countries\n\n` +
        `🎮 *Commands:*\n` +
        `• \`.emojipuzzle\` - Random puzzle\n` +
        `• \`.emojipuzzle [category]\` - Specific category\n` +
        `• \`.emojipuzzle stats\` - Your statistics`
      )
    }

    if (match === 'stats') {
      const stats = partyGameStats.get(player)
      if (!stats) {
        return await message.send(`📊 *No emoji puzzle stats yet!* Play some games first with \`.emojipuzzle\``)
      }

      return await message.send(
        `🧩 *Your Emoji Puzzle Stats*\n\n` +
        `🎯 *Puzzles solved:* ${stats.puzzlesSolved}\n` +
        `⚡ *Speed solves:* ${stats.speedSolves || 0}\n` +
        `💡 *No-hint solves:* ${stats.noHintSolves || 0}\n` +
        `⭐ *Total points:* ${stats.totalPoints}\n` +
        `🏆 *Achievements:* ${(partyAchievements.get(player) || []).length}\n\n` +
        `💡 *Keep solving to unlock more achievements!*`,
        { mentions: [player] }
      )
    }

    const moviePuzzles = [
      { emojis: "🦁👑", answer: "The Lion King", hint: "Disney classic about a young lion", difficulty: "easy" },
      { emojis: "🕷️👨", answer: "Spider-Man", hint: "Marvel superhero with web powers", difficulty: "easy" },
      { emojis: "❄️👸", answer: "Frozen", hint: "Let it go...", difficulty: "easy" },
      { emojis: "🏰👸🐸", answer: "The Princess and the Frog", hint: "Disney princess story", difficulty: "medium" },
      { emojis: "🦈🌊", answer: "Jaws", hint: "Classic shark thriller", difficulty: "easy" },
      { emojis: "👻💀", answer: "Ghostbusters", hint: "Who you gonna call?", difficulty: "easy" },
      { emojis: "🚗⚡", answer: "Cars", hint: "Pixar racing movie", difficulty: "easy" },
      { emojis: "🐠🔍", answer: "Finding Nemo", hint: "Lost fish adventure", difficulty: "easy" },
      { emojis: "🌟⚔️", answer: "Star Wars", hint: "Space saga with lightsabers", difficulty: "medium" },
      { emojis: "🕸️🕷️🏙️", answer: "Spider-Man", hint: "Web-slinging superhero", difficulty: "medium" },
      { emojis: "🦇🌃👨", answer: "Batman", hint: "Dark Knight of Gotham", difficulty: "medium" },
      { emojis: "🧙‍♂️⚡👓", answer: "Harry Potter", hint: "Boy wizard with lightning scar", difficulty: "hard" }
    ]
    
    const songPuzzles = [
      { emojis: "🌟⭐", answer: "Twinkle Twinkle Little Star", hint: "Children's lullaby", difficulty: "easy" },
      { emojis: "💔😢", answer: "Someone Like You", hint: "Adele hit song", difficulty: "medium" },
      { emojis: "🌈🎵", answer: "Somewhere Over the Rainbow", hint: "Wizard of Oz classic", difficulty: "medium" },
      { emojis: "🕺💃", answer: "Dancing Queen", hint: "ABBA disco hit", difficulty: "easy" },
      { emojis: "🌙🚶‍♂️", answer: "Moonwalk", hint: "Michael Jackson move", difficulty: "hard" },
      { emojis: "🔥❤️", answer: "Burning Love", hint: "Elvis Presley song", difficulty: "medium" },
      { emojis: "🎸⚡", answer: "Thunderstruck", hint: "AC/DC rock anthem", difficulty: "hard" },
      { emojis: "👑🐝", answer: "Queen Bee", hint: "Beyoncé nickname", difficulty: "medium" }
    ]

    const animalPuzzles = [
      { emojis: "🦓", answer: "Zebra", hint: "Black and white striped horse", difficulty: "easy" },
      { emojis: "🐧❄️", answer: "Penguin", hint: "Antarctic bird that can't fly", difficulty: "easy" },
      { emojis: "🦒🌿", answer: "Giraffe", hint: "Tallest animal in the world", difficulty: "easy" },
      { emojis: "🐨🌿", answer: "Koala", hint: "Australian marsupial", difficulty: "medium" },
      { emojis: "🦏🏃‍♂️", answer: "Rhino", hint: "Large horned mammal", difficulty: "medium" },
      { emojis: "🦅🏔️", answer: "Eagle", hint: "Majestic bird of prey", difficulty: "easy" },
      { emojis: "🐙🌊", answer: "Octopus", hint: "Eight-armed sea creature", difficulty: "easy" }
    ]

    const foodPuzzles = [
      { emojis: "🍕🧀", answer: "Pizza", hint: "Italian flatbread with toppings", difficulty: "easy" },
      { emojis: "🍔🍟", answer: "Burger and Fries", hint: "Fast food combo", difficulty: "easy" },
      { emojis: "🍝🍅", answer: "Spaghetti", hint: "Long pasta with sauce", difficulty: "easy" },
      { emojis: "🍣🐟", answer: "Sushi", hint: "Japanese raw fish dish", difficulty: "medium" },
      { emojis: "🌮🌶️", answer: "Tacos", hint: "Mexican folded tortilla", difficulty: "easy" },
      { emojis: "🥐🇫🇷", answer: "Croissant", hint: "French pastry", difficulty: "medium" },
      { emojis: "🍜🥢", answer: "Ramen", hint: "Japanese noodle soup", difficulty: "medium" }
    ]

    const brandPuzzles = [
      { emojis: "🍎📱", answer: "Apple", hint: "Tech company with fruit logo", difficulty: "easy" },
      { emojis: "🏃‍♂️✅", answer: "Nike", hint: "Just Do It", difficulty: "easy" },
      { emojis: "☕🟢", answer: "Starbucks", hint: "Coffee chain with mermaid logo", difficulty: "easy" },
      { emojis: "🍔👑", answer: "Burger King", hint: "Have it your way", difficulty: "medium" },
      { emojis: "🎬🔴", answer: "Netflix", hint: "Streaming service", difficulty: "easy" },
      { emojis: "🚗🏎️", answer: "Ferrari", hint: "Italian luxury sports car", difficulty: "medium" }
    ]

    const countryPuzzles = [
      { emojis: "🗽🦅", answer: "USA", hint: "Land of the free", difficulty: "easy" },
      { emojis: "🍕🍝", answer: "Italy", hint: "Boot-shaped country", difficulty: "easy" },
      { emojis: "🥖🗼", answer: "France", hint: "City of lights", difficulty: "easy" },
      { emojis: "🍣⛩️", answer: "Japan", hint: "Land of the rising sun", difficulty: "medium" },
      { emojis: "🐨🦘", answer: "Australia", hint: "Down under", difficulty: "easy" },
      { emojis: "🏔️🧀", answer: "Switzerland", hint: "Neutral country with mountains", difficulty: "medium" }
    ]
    
    let puzzles, category
    switch (match) {
      case 'movies':
        puzzles = moviePuzzles
        category = 'Movies 🎬'
        break
      case 'songs':
        puzzles = songPuzzles
        category = 'Songs 🎵'
        break
      case 'animals':
        puzzles = animalPuzzles
        category = 'Animals 🐾'
        break
      case 'food':
        puzzles = foodPuzzles
        category = 'Food 🍕'
        break
      case 'brands':
        puzzles = brandPuzzles
        category = 'Brands 🏷️'
        break
      case 'countries':
        puzzles = countryPuzzles
        category = 'Countries 🌍'
        break
      default:
        puzzles = [...moviePuzzles, ...songPuzzles, ...animalPuzzles, ...foodPuzzles, ...brandPuzzles, ...countryPuzzles]
        category = 'Mixed 🎲'
    }

    const randomPuzzle = puzzles[Math.floor(Math.random() * puzzles.length)]
    updatePartyStats(player, 'puzzle', 'started', 3)

    // Store active puzzle for answer checking
    activePartyGames.set(message.jid, {
      type: 'puzzle',
      puzzle: randomPuzzle,
      category: category,
      startTime: Date.now(),
      hintUsed: false,
      starter: player
    })

    const difficultyEmoji = randomPuzzle.difficulty === 'easy' ? '🟢' : randomPuzzle.difficulty === 'medium' ? '🟡' : '🔴'

    return await message.send(
      `🧩 *EMOJI PUZZLE* - ${category}\n\n` +
      `${randomPuzzle.emojis}\n\n` +
      `🤔 *What am I?*\n\n` +
      `${difficultyEmoji} *Difficulty:* ${randomPuzzle.difficulty.toUpperCase()}\n` +
      `💰 *Points:* ${randomPuzzle.difficulty === 'easy' ? '10' : randomPuzzle.difficulty === 'medium' ? '15' : '20'}\n\n` +
      `⏰ *First correct answer wins!*\n` +
      `💡 *Type "hint" for a clue*\n\n` +
      `🎯 *Categories:* movies, songs, animals, food, brands, countries`
    )
  }
)

// Emoji puzzle answer handler
bot(
  {
    pattern: '.*',
    fromMe: false,
    dontAddCommandList: true,
  },
  async (message, match) => {
    const gameId = message.jid
    const player = message.participant || message.sender
    const game = activePartyGames.get(gameId)

    if (!game || game.type !== 'puzzle') return

    const guess = match.trim().toLowerCase()
    const answer = game.puzzle.answer.toLowerCase()

    // Check for hint request
    if (guess === 'hint' && !game.hintUsed) {
      game.hintUsed = true
      return await message.send(
        `💡 *HINT:* ${game.puzzle.hint}\n\n` +
        `🧩 ${game.puzzle.emojis}\n\n` +
        `🤔 *What am I?*\n` +
        `⚠️ *Hint used - points reduced by 3*`
      )
    }

    // Check if answer is correct
    if (guess === answer || guess.includes(answer.split(' ')[0])) {
      const timeTaken = Date.now() - game.startTime
      const speedBonus = timeTaken < 30000 ? 5 : 0
      const hintPenalty = game.hintUsed ? 3 : 0
      const difficultyPoints = game.puzzle.difficulty === 'easy' ? 10 : game.puzzle.difficulty === 'medium' ? 15 : 20
      const totalPoints = difficultyPoints + speedBonus - hintPenalty

      updatePartyStats(player, 'puzzle', 'solved', totalPoints)

      // Check for speed achievement
      if (speedBonus > 0) {
        const stats = partyGameStats.get(player)
        stats.speedSolves = (stats.speedSolves || 0) + 1
        partyGameStats.set(player, stats)
      }

      // Check for no-hint achievement
      if (!game.hintUsed) {
        const stats = partyGameStats.get(player)
        stats.noHintSolves = (stats.noHintSolves || 0) + 1
        partyGameStats.set(player, stats)
      }

      activePartyGames.delete(gameId)

      return await message.send(
        `🎉 *CORRECT!* @${player.split('@')[0]}\n\n` +
        `✅ *Answer:* ${game.puzzle.answer}\n` +
        `⏱️ *Time:* ${Math.round(timeTaken / 1000)}s\n` +
        `💰 *Points earned:* ${totalPoints}\n` +
        `${speedBonus > 0 ? '⚡ *Speed bonus: +5*\n' : ''}` +
        `${game.hintUsed ? '💡 *Hint penalty: -3*\n' : '🎯 *No hint bonus!*\n'}` +
        `🏆 *Total puzzles solved:* ${partyGameStats.get(player)?.puzzlesSolved || 1}\n\n` +
        `🎮 *Play again with* \`.emojipuzzle\``,
        { mentions: [player] }
      )
    }
  }
)

// Quick Math Race
bot(
  {
    pattern: 'mathrace ?(easy|medium|hard)?',
    desc: 'Quick math problems - race to solve first!',
    type: 'game',
  },
  async (message, match) => {
    const difficulty = match || 'easy'
    let num1, num2, operation, answer, problem
    
    switch (difficulty) {
      case 'easy':
        num1 = Math.floor(Math.random() * 20) + 1
        num2 = Math.floor(Math.random() * 20) + 1
        operation = ['+', '-'][Math.floor(Math.random() * 2)]
        break
      case 'medium':
        num1 = Math.floor(Math.random() * 50) + 10
        num2 = Math.floor(Math.random() * 50) + 10
        operation = ['+', '-', '×'][Math.floor(Math.random() * 3)]
        break
      case 'hard':
        num1 = Math.floor(Math.random() * 100) + 20
        num2 = Math.floor(Math.random() * 100) + 20
        operation = ['+', '-', '×', '÷'][Math.floor(Math.random() * 4)]
        break
    }
    
    switch (operation) {
      case '+':
        answer = num1 + num2
        problem = `${num1} + ${num2}`
        break
      case '-':
        answer = num1 - num2
        problem = `${num1} - ${num2}`
        break
      case '×':
        answer = num1 * num2
        problem = `${num1} × ${num2}`
        break
      case '÷':
        // Ensure clean division
        answer = num1
        num1 = num1 * num2
        problem = `${num1} ÷ ${num2}`
        break
    }
    
    return await message.send(
      `🧮 *MATH RACE* - ${difficulty.toUpperCase()}\n\n` +
      `📊 *Solve this:*\n\n` +
      `**${problem} = ?**\n\n` +
      `⚡ *First correct answer wins!*\n` +
      `🏆 *Just type the number*\n\n` +
      `💡 *Difficulties:* easy, medium, hard`
    )
  }
)

// Random Group Activity Suggestions
bot(
  {
    pattern: 'groupactivity',
    desc: 'Get random group activity suggestions',
    type: 'game',
  },
  async (message) => {
    const activities = [
      "🎵 Share your current favorite song",
      "📸 Everyone send a selfie with a funny face",
      "🍕 Debate: Pineapple on pizza - yes or no?",
      "🎬 Recommend a movie everyone should watch",
      "📱 Share your most used emoji",
      "🎨 Describe your dream vacation in 3 words",
      "🎭 Everyone share their hidden talent",
      "📚 What's the last book you read?",
      "🎮 Favorite childhood game?",
      "🌟 Share a random fun fact about yourself",
      "🎪 If you could have any superpower, what would it be?",
      "🍔 What's your go-to comfort food?",
      "🎵 Sing a line from a song and others guess it",
      "📱 Share your phone's wallpaper",
      "🎨 Draw something and share it (even if it's bad!)"
    ]
    
    const randomActivity = activities[Math.floor(Math.random() * activities.length)]
    
    return await message.send(
      `🎉 *GROUP ACTIVITY TIME!*\n\n` +
      `${randomActivity}\n\n` +
      `👥 *Everyone participate!*\n` +
      `🔄 *Type* \`.groupactivity\` *for another idea*`
    )
  }
)
