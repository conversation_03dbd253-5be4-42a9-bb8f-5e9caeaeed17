#!/usr/bin/env node

/**
 * 🔄 Session Reset Script
 * 
 * Use this when you get logout errors
 */

const fs = require('fs')
const path = require('path')

console.log('🔄 Resetting Sessions to Fix Logout Issues')
console.log('==========================================\n')

// Stop any running processes
console.log('🛑 Stopping any running bot processes...')
const { exec } = require('child_process')

exec('pkill -f "node index.js"', (error) => {
  if (error) {
    console.log('   No running processes found')
  } else {
    console.log('   ✅ Stopped running processes')
  }
})

// Wait 5 seconds then clean
setTimeout(() => {
  // Clean auth directories
  const authDir = path.join(__dirname, 'auth')
  const sessionDirs = ['session1', 'session2']
  
  sessionDirs.forEach(sessionName => {
    const sessionPath = path.join(authDir, sessionName)
    
    if (fs.existsSync(sessionPath)) {
      console.log(`🧹 Cleaning ${sessionName} auth files...`)
      
      try {
        const files = fs.readdirSync(sessionPath)
        files.forEach(file => {
          fs.unlinkSync(path.join(sessionPath, file))
        })
        console.log(`   ✅ Cleaned ${sessionName}`)
      } catch (error) {
        console.log(`   ⚠️ Could not clean ${sessionName}`)
      }
    }
  })
  
  console.log('\n✅ Session reset complete!')
  console.log('\n📝 Next Steps:')
  console.log('   1. Wait 5 minutes before restarting')
  console.log('   2. Use: node start-dual-sessions-fixed.js')
  console.log('   3. Scan QR codes with 2-minute gap between them')
  console.log('   4. Let both sessions connect fully before using commands')
  
}, 5000)
