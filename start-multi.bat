@echo off
echo 🤖 Levanter Multi-Session Starter
echo =================================

REM Check if PM2 is installed
where pm2 >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ PM2 is not installed. Installing PM2...
    npm install -g pm2
)

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

REM Check if config.json exists
if not exist "config.json" (
    echo ❌ config.json not found. Please create it with your session configurations.
    pause
    exit /b 1
)

REM Stop any existing sessions
echo 🛑 Stopping existing sessions...
pm2 delete ecosystem.config.js >nul 2>&1

REM Start all sessions using PM2
echo 🚀 Starting all sessions...
pm2 start ecosystem.config.js

REM Show status
echo.
echo 📊 Session Status:
pm2 status

echo.
echo 📝 Useful Commands:
echo    pm2 status                    - Show all sessions status
echo    pm2 logs                      - Show all logs
echo    pm2 logs levanter-session1    - Show specific session logs
echo    pm2 restart all               - Restart all sessions
echo    pm2 stop all                  - Stop all sessions
echo    pm2 delete all                - Delete all sessions
echo.
echo ✅ Multi-session setup complete!
pause
