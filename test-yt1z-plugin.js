#!/usr/bin/env node

/**
 * 🧪 Test YT1Z.net/en/ Plugin
 * 
 * This script tests the YT1Z plugin to make sure it works with yt1z.net/en/
 */

const axios = require('axios')

console.log('🧪 Testing YT1Z.net/en/ Plugin')
console.log('==============================\n')

// Test video ID (Rick Roll - short and reliable)
const testVideoId = 'dQw4w9WgXcQ'
const testVideoUrl = `https://www.youtube.com/watch?v=${testVideoId}`

// YT1Z Downloader class (same as in plugin)
class YT1ZDownloader {
  constructor() {
    this.baseUrl = 'https://yt1z.net/en'
    this.apiUrl = 'https://yt1z.net/en/api/button/mp3'
  }

  extractVideoId(url) {
    const ytIdRegex = /(?:http(?:s|):\/\/|)(?:(?:www\.|)youtube(?:\-nocookie|)\.com\/(?:watch\?.*(?:|\&)v=|embed|shorts\/|v\/)|youtu\.be\/)([-_0-9A-Za-z]{11})/
    const match = ytIdRegex.exec(url)
    return match ? match[1] : null
  }

  async testConnection() {
    try {
      console.log('🌐 Testing connection to yt1z.net/en/...')
      const response = await axios.get('https://yt1z.net/en/', {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })
      
      if (response.status === 200) {
        console.log('✅ Connection successful')
        console.log(`   Status: ${response.status}`)
        console.log(`   Content length: ${response.data.length} characters`)
        
        // Check if it's the right page
        if (response.data.includes('YouTube') || response.data.includes('yt1z')) {
          console.log('✅ Correct website detected')
          return true
        } else {
          console.log('⚠️ Website content unexpected')
          return false
        }
      } else {
        console.log('❌ Connection failed')
        return false
      }
    } catch (error) {
      console.log('❌ Connection error:', error.message)
      return false
    }
  }

  async testSubmission() {
    try {
      console.log('\n📤 Testing URL submission to yt1z.net/en/...')
      
      const formData = new URLSearchParams()
      formData.append('url', testVideoUrl)
      
      const response = await axios.post('https://yt1z.net/en/', formData, {
        timeout: 15000,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Referer': 'https://yt1z.net/en/',
          'Origin': 'https://yt1z.net'
        }
      })
      
      if (response.status === 200) {
        console.log('✅ URL submission successful')
        console.log(`   Response length: ${response.data.length} characters`)
        
        // Check for download links in response
        const html = response.data
        const hasDownloadLink = html.includes('download') || 
                               html.includes('.mp3') || 
                               html.includes('Download')
        
        if (hasDownloadLink) {
          console.log('✅ Download links detected in response')
          
          // Try to extract a download link
          const mp3Patterns = [
            /href="([^"]*\.mp3[^"]*)"[^>]*>.*?download/i,
            /href="([^"]*download[^"]*mp3[^"]*)"[^>]*>/i,
            /"downloadUrl":"([^"]*\.mp3[^"]*)"/,
            /"url":"([^"]*\.mp3[^"]*)"/
          ]
          
          for (const pattern of mp3Patterns) {
            const match = html.match(pattern)
            if (match && match[1]) {
              console.log('✅ Download link found!')
              console.log(`   Link: ${match[1].substring(0, 80)}...`)
              return true
            }
          }
          
          console.log('⚠️ Download links present but pattern not matched')
          console.log('   This might need manual adjustment')
          return true
        } else {
          console.log('❌ No download links found in response')
          return false
        }
      } else {
        console.log('❌ URL submission failed')
        return false
      }
    } catch (error) {
      console.log('❌ Submission error:', error.message)
      return false
    }
  }
}

async function runTests() {
  console.log(`🎯 Testing with video: ${testVideoUrl}\n`)
  
  const downloader = new YT1ZDownloader()
  
  const results = {
    connection: await downloader.testConnection(),
    submission: await downloader.testSubmission()
  }
  
  console.log('\n📊 Test Results Summary:')
  console.log('========================')
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL'
    const testName = test.charAt(0).toUpperCase() + test.slice(1)
    console.log(`   ${testName}: ${status}`)
  })
  
  const passedTests = Object.values(results).filter(Boolean).length
  const totalTests = Object.keys(results).length
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`)
  
  if (passedTests >= 1) {
    console.log('\n🎉 YT1Z plugin should work!')
    console.log('\n📝 Usage:')
    console.log('   .yt1z https://youtu.be/dQw4w9WgXcQ')
    console.log('   .yt1z never gonna give you up auto')
    console.log('   .yt1z despacito')
    
    console.log('\n🚀 To test in your bot:')
    console.log('   1. Restart: node start-dual-sessions-fixed.js')
    console.log('   2. Try: .yt1z test auto')
  } else {
    console.log('\n⚠️ Issues detected with yt1z.net/en/')
    console.log('\n💡 Possible reasons:')
    console.log('   • Website might be down')
    console.log('   • They changed their API structure')
    console.log('   • Network connectivity issues')
    console.log('   • Rate limiting or blocking')
    
    console.log('\n🔧 Solutions:')
    console.log('   • Try again later')
    console.log('   • Use VPN if blocked')
    console.log('   • Use alternative: .ytdl or .play commands')
  }
}

// Run the tests
runTests().catch(error => {
  console.error('\n💥 Test failed:', error.message)
  process.exit(1)
})
