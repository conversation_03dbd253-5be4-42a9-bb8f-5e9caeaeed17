#!/usr/bin/env node

/**
 * 🧪 Test Script for Enhanced YTDL Plugin
 * 
 * This script tests the YouTube download functionality
 * to make sure everything works before using it in WhatsApp
 */

console.log('🧪 Testing Enhanced YTDL Plugin')
console.log('===============================\n')

// Test video ID (Rick Roll - short and reliable)
const testVideoId = 'dQw4w9WgXcQ'
const testVideoUrl = `https://www.youtube.com/watch?v=${testVideoId}`

async function testYTSSearch() {
  console.log('🔍 Testing YouTube Search (yts)...')
  try {
    const { yts } = require('./lib/')
    
    // Test search
    const searchResults = await yts('never gonna give you up', false, null, 'test')
    if (searchResults && searchResults.length > 0) {
      console.log(`✅ Search SUCCESS: Found ${searchResults.length} results`)
      console.log(`   First result: ${searchResults[0].title}`)
      return true
    } else {
      console.log('❌ Search FAILED: No results returned')
      return false
    }
  } catch (error) {
    console.log('❌ Search ERROR:', error.message)
    return false
  }
}

async function testYTSVideoInfo() {
  console.log('\n📋 Testing Video Info (yts)...')
  try {
    const { yts } = require('./lib/')
    
    // Test video info
    const [videoInfo] = await yts(testVideoId, true, null, 'test')
    if (videoInfo && videoInfo.title) {
      console.log('✅ Video Info SUCCESS')
      console.log(`   Title: ${videoInfo.title}`)
      console.log(`   Author: ${videoInfo.author}`)
      console.log(`   Duration: ${videoInfo.duration}`)
      return true
    } else {
      console.log('❌ Video Info FAILED: No info returned')
      return false
    }
  } catch (error) {
    console.log('❌ Video Info ERROR:', error.message)
    return false
  }
}

async function testY2MateVideo() {
  console.log('\n🎬 Testing Y2Mate Video Download...')
  try {
    const { y2mate, isUrl } = require('./lib/')
    
    // Test video download
    const result = await y2mate.get(testVideoId, 'video', '360p')
    if (result && isUrl(result)) {
      console.log('✅ Y2Mate Video SUCCESS')
      console.log(`   Download URL: ${result.substring(0, 50)}...`)
      return true
    } else {
      console.log('❌ Y2Mate Video FAILED: No download URL')
      console.log('   Response:', result)
      return false
    }
  } catch (error) {
    console.log('❌ Y2Mate Video ERROR:', error.message)
    return false
  }
}

async function testY2MateAudio() {
  console.log('\n🎵 Testing Y2Mate Audio Download...')
  try {
    const { y2mate, isUrl } = require('./lib/')
    
    // Test audio download
    const result = await y2mate.get(testVideoId, 'audio')
    if (result && isUrl(result)) {
      console.log('✅ Y2Mate Audio SUCCESS')
      console.log(`   Download URL: ${result.substring(0, 50)}...`)
      return true
    } else {
      console.log('❌ Y2Mate Audio FAILED: No download URL')
      console.log('   Response:', result)
      return false
    }
  } catch (error) {
    console.log('❌ Y2Mate Audio ERROR:', error.message)
    return false
  }
}

async function testYouTubeCookie() {
  console.log('\n🍪 Testing YouTube Cookie Configuration...')
  
  const ytCookie = process.env.YT_COOKIE || process.env.YOUTUBE_COOKIE || ''
  
  if (ytCookie && ytCookie.length > 50) {
    console.log('✅ YouTube Cookie FOUND')
    console.log(`   Length: ${ytCookie.length} characters`)
    console.log(`   Preview: ${ytCookie.substring(0, 50)}...`)
    return true
  } else {
    console.log('⚠️ YouTube Cookie NOT FOUND or too short')
    console.log('   This may limit download success rate')
    console.log('   You can add YT_COOKIE to your config.env file')
    return false
  }
}

async function runAllTests() {
  console.log(`🎯 Testing with video: ${testVideoUrl}\n`)
  
  const results = {
    cookie: await testYouTubeCookie(),
    search: await testYTSSearch(),
    videoInfo: await testYTSVideoInfo(),
    videoDownload: await testY2MateVideo(),
    audioDownload: await testY2MateAudio()
  }
  
  console.log('\n📊 Test Results Summary:')
  console.log('========================')
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL'
    const testName = test.charAt(0).toUpperCase() + test.slice(1)
    console.log(`   ${testName}: ${status}`)
  })
  
  const passedTests = Object.values(results).filter(Boolean).length
  const totalTests = Object.keys(results).length
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`)
  
  if (passedTests >= 3) {
    console.log('\n🎉 Plugin should work well!')
    console.log('\n📝 Usage Examples:')
    console.log('   .ytdl https://youtu.be/dQw4w9WgXcQ')
    console.log('   .ytdl never gonna give you up auto')
    console.log('   .ytdl audio despacito')
    console.log('   .ytdl audio jingle bells auto')
  } else {
    console.log('\n⚠️ Some issues detected. Check the errors above.')
    console.log('\n💡 Troubleshooting:')
    console.log('   • Make sure you have internet connection')
    console.log('   • Check if Y2Mate service is accessible')
    console.log('   • Add YouTube cookie to config.env if needed')
  }
}

// Run the tests
runAllTests().catch(error => {
  console.error('\n💥 Test script failed:', error.message)
  process.exit(1)
})
