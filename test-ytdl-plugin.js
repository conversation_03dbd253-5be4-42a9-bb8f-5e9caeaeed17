#!/usr/bin/env node

/**
 * 🧪 Simple Test Script for Enhanced YTDL Plugin
 *
 * This script tests basic functionality without complex dependencies
 */

console.log('🧪 Testing Enhanced YTDL Plugin')
console.log('===============================\n')

// Test video ID (Rick Roll - short and reliable)
const testVideoId = 'dQw4w9WgXcQ'
const testVideoUrl = `https://www.youtube.com/watch?v=${testVideoId}`

async function testBasicRequirements() {
  console.log('🔍 Testing Basic Requirements...')

  try {
    // Test if Node.js modules work
    const fs = require('fs')
    const path = require('path')

    console.log('✅ Node.js modules: Working')

    // Check if plugin file exists
    const pluginPath = path.join(__dirname, 'plugins', 'ytdl-enhanced.js')
    if (fs.existsSync(pluginPath)) {
      console.log('✅ Plugin file: Found')
    } else {
      console.log('❌ Plugin file: Missing')
      return false
    }

    // Check if config files exist
    const configJsonPath = path.join(__dirname, 'config.json')
    const configEnvPath = path.join(__dirname, 'config.env')

    if (fs.existsSync(configJsonPath)) {
      console.log('✅ config.json: Found')
    } else {
      console.log('⚠️ config.json: Missing')
    }

    if (fs.existsSync(configEnvPath)) {
      console.log('✅ config.env: Found')
    } else {
      console.log('⚠️ config.env: Missing')
    }

    return true
  } catch (error) {
    console.log('❌ Basic Requirements ERROR:', error.message)
    return false
  }
}

async function testYouTubeCookie() {
  console.log('\n🍪 Testing YouTube Cookie Configuration...')

  try {
    // Check environment variables
    const ytCookie = process.env.YT_COOKIE || process.env.YOUTUBE_COOKIE || ''

    if (ytCookie && ytCookie.length > 50) {
      console.log('✅ YouTube Cookie FOUND')
      console.log(`   Length: ${ytCookie.length} characters`)
      console.log(`   Preview: ${ytCookie.substring(0, 50)}...`)
      return true
    } else {
      console.log('⚠️ YouTube Cookie NOT FOUND or too short')
      console.log('   This may limit download success rate')
      console.log('   You can add YT_COOKIE to your config.env file')
      return false
    }
  } catch (error) {
    console.log('❌ Cookie Test ERROR:', error.message)
    return false
  }
}

async function testConfigFiles() {
  console.log('\n📋 Testing Configuration Files...')

  try {
    const fs = require('fs')
    const path = require('path')

    // Test config.json
    const configJsonPath = path.join(__dirname, 'config.json')
    if (fs.existsSync(configJsonPath)) {
      const configJson = JSON.parse(fs.readFileSync(configJsonPath, 'utf8'))

      // Check for valid sessions
      const validSessions = Object.keys(configJson).filter(sessionName => {
        const config = configJson[sessionName]
        return config.SESSION_ID &&
               config.SESSION_ID !== 'levanter_sessionid' &&
               config.SESSION_ID.length > 20
      })

      console.log(`✅ Valid sessions found: ${validSessions.length}`)
      validSessions.forEach(session => {
        console.log(`   • ${session}: ${configJson[session].SESSION_ID.substring(0, 20)}...`)
      })

      return validSessions.length > 0
    } else {
      console.log('❌ config.json not found')
      return false
    }
  } catch (error) {
    console.log('❌ Config Test ERROR:', error.message)
    return false
  }
}

async function testInternetConnection() {
  console.log('\n🌐 Testing Internet Connection...')

  try {
    const https = require('https')

    return new Promise((resolve) => {
      const req = https.get('https://www.google.com', (res) => {
        if (res.statusCode === 200) {
          console.log('✅ Internet connection: Working')
          resolve(true)
        } else {
          console.log('❌ Internet connection: Failed')
          resolve(false)
        }
      })

      req.on('error', () => {
        console.log('❌ Internet connection: Failed')
        resolve(false)
      })

      req.setTimeout(5000, () => {
        console.log('❌ Internet connection: Timeout')
        req.destroy()
        resolve(false)
      })
    })
  } catch (error) {
    console.log('❌ Internet Test ERROR:', error.message)
    return false
  }
}

async function runAllTests() {
  console.log(`🎯 Testing plugin setup for: ${testVideoUrl}\n`)

  const results = {
    requirements: await testBasicRequirements(),
    config: await testConfigFiles(),
    cookie: await testYouTubeCookie(),
    internet: await testInternetConnection()
  }

  console.log('\n📊 Test Results Summary:')
  console.log('========================')

  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL'
    const testName = test.charAt(0).toUpperCase() + test.slice(1)
    console.log(`   ${testName}: ${status}`)
  })

  const passedTests = Object.values(results).filter(Boolean).length
  const totalTests = Object.keys(results).length

  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`)

  if (passedTests >= 3) {
    console.log('\n🎉 Plugin setup looks good!')
    console.log('\n📝 Next Steps:')
    console.log('   1. Restart your bot: node start-dual-sessions.js')
    console.log('   2. Try the plugin: .ytdl never gonna give you up auto')
    console.log('\n💡 Usage Examples:')
    console.log('   .ytdl https://youtu.be/dQw4w9WgXcQ')
    console.log('   .ytdl never gonna give you up auto')
    console.log('   .ytdl audio despacito')
    console.log('   .ytdl audio jingle bells auto')
  } else {
    console.log('\n⚠️ Some issues detected. Please fix them before using the plugin.')
    console.log('\n💡 Troubleshooting:')
    console.log('   • Make sure you have internet connection')
    console.log('   • Check if config.json has valid SESSION_IDs')
    console.log('   • Add YouTube cookie to config.env if needed')
    console.log('   • Make sure the plugin file exists in plugins/ folder')
  }
}

// Run the tests
runAllTests().catch(error => {
  console.error('\n💥 Test script failed:', error.message)
  process.exit(1)
})
