/**
 * Enhanced YouTube Downloader Plugin
 *
 * This plugin provides robust YouTube video and audio downloading with multiple fallback methods:
 * - Y2mate API (primary method)
 * - Cobalt Tools API
 * - YT-DLP API
 * - SaveFrom API
 * - YouTube.js API
 *
 * Commands:
 * - .ytdl <url/search> - Download video with multiple quality options
 * - .ytmp3 <url/search> - Download audio in MP3 format
 *
 * Features:
 * - Auto-download with "auto" keyword
 * - Search functionality with interactive selection
 * - Multiple API fallbacks for reliability
 * - Detailed error messages and troubleshooting tips
 */

const { bot, yts, y2mate, getBuffer, isUrl, generateList } = require('../lib/')
const axios = require('axios')

const ytIdRegex = /(?:http(?:s|):\/\/|)(?:(?:www\.|)youtube(?:\-nocookie|)\.com\/(?:watch\?.*(?:|\&)v=|embed|shorts\/|v\/)|youtu\.be\/)([-_0-9A-Za-z]{11})/

// YouTube cookie for better access (you can set this if you have one)
const YOUTUBE_COOKIE = process.env.YOUTUBE_COOKIE || ''

// Enhanced YouTube Video Downloader with multiple fallback methods
bot(
  {
    pattern: 'ytdl ?(.*)',
    desc: 'Enhanced YouTube video downloader with multiple methods',
    type: 'download',
  },
  async (message, match) => {
    match = match || message.reply_message.text
    if (!match) return await message.send('*📥 Enhanced YouTube Downloader*\n\n*Usage:*\n• `.ytdl <YouTube URL>`\n• `.ytdl <search term>`\n• `.ytdl <search term> auto` - Auto download first result\n\n*Example:*\n`.ytdl https://youtu.be/dQw4w9WgXcQ`\n`.ytdl jingle bells auto`')

    const vid = ytIdRegex.exec(match)
    
    // If not a direct URL, search first
    if (!vid) {
      try {
        const result = await yts(match, false, null, message.id)
        if (!result.length) return await message.send(`❌ *No results found for:* ${match}`)
        
        // Check if user wants auto-download
        if (match.includes(' auto') || match.includes(' first') || match.includes(' download')) {
          const searchTerm = match.replace(/ (auto|first|download)/g, '').trim()
          const topResult = result[0]
          
          await message.send(
            `🎯 *Auto-downloading first result for:* ${searchTerm}\n\n` +
            `🎬 *${topResult.title}*\n` +
            `👤 ${topResult.author}\n` +
            `⏱️ ${topResult.duration} | 👁️ ${topResult.view}\n\n` +
            `🔄 *Starting download...*`,
            { quoted: message.data }
          )
          
          // Continue with download using first result
          return await downloadVideo(message, topResult.id, topResult)
        } else {
          // Show selection list
          const msg = generateList(
            result.slice(0, 10).map(({ title, id, duration, view, author }) => ({
              text: `🎬 ${title}\n⏱️ ${duration} | 👁️ ${view}\n👤 ${author}\n`,
              id: `ytdl https://www.youtube.com/watch?v=${id}`,
            })),
            `🔍 *Search Results for:* ${match}\n\n` +
            `💡 *Tip:* Add "auto" to search term for instant download\n` +
            `Example: \`.ytdl ${match} auto\`\n\n` +
            `Select a video to download:`,
            message.jid,
            message.participant,
            message.id
          )
          return await message.send(msg.message, { quoted: message.data }, msg.type)
        }
      } catch (error) {
        return await message.send(`❌ *Search failed:* ${error.message}`)
      }
    }

    const videoId = vid[1]
    
    // Get video info first
    let videoInfo
    try {
      const [info] = await yts(videoId, true, null, message.id)
      videoInfo = info
    } catch (error) {
      return await message.send(`❌ *Failed to get video info:* ${error.message}`)
    }

    return await downloadVideo(message, videoId, videoInfo)
  }
)

// Function to handle video download with multiple methods
async function downloadVideo(message, videoId, videoInfo) {
  const { title, duration, view, author, thumbnail } = videoInfo
  const videoUrl = `https://www.youtube.com/watch?v=${videoId}`

  // Safely handle undefined values
  const safeTitle = title || 'Unknown Title'
  const safeDuration = duration || 'Unknown'
  const safeView = view || 'Unknown'
  const safeAuthor = author || 'Unknown Channel'

  // Send initial message with video info
  await message.send(
    `📥 *Downloading Video...*\n\n` +
    `🎬 *Title:* ${safeTitle}\n` +
    `⏱️ *Duration:* ${safeDuration}\n` +
    `👁️ *Views:* ${safeView}\n` +
    `👤 *Author:* ${safeAuthor}\n\n` +
    `🔄 *Processing... Please wait*`,
    { quoted: message.data }
  )

  // Method 1: Try Y2mate
  try {
    await message.send('🔄 *Trying Method 1: Y2mate...*')
    
    const y2mateResult = await y2mate.get(videoId, 'video')
    
    if (isUrl(y2mateResult)) {
      await message.send('✅ *Method 1 successful! Sending video...*')
      return await message.sendFromUrl(y2mateResult, {
        quoted: message.data,
        caption: `🎬 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}`
      })
    }
    
    if (y2mateResult && y2mateResult.video) {
      // Get the best quality available
      const qualities = Object.keys(y2mateResult.video)
      const bestQuality = qualities.includes('720p') ? '720p' : 
                         qualities.includes('480p') ? '480p' : 
                         qualities.includes('360p') ? '360p' : qualities[0]
      
      if (bestQuality) {
        const downloadUrl = await y2mate.dl(videoId, 'video', bestQuality)
        if (downloadUrl) {
          await message.send('✅ *Method 1 successful! Sending video...*')
          return await message.sendFromUrl(downloadUrl, {
            quoted: message.data,
            caption: `🎬 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n📺 Quality: ${bestQuality}`
          })
        }
      }
    }
  } catch (error) {
    await message.send(`❌ *Method 1 failed:* ${error.message}`)
  }

  // Method 2: Try working Cobalt API
  try {
    await message.send('🔄 *Trying Method 2: Cobalt API...*')

    const apiUrl = `https://co.wuk.sh/api/json`
    const response = await axios.post(apiUrl, {
      url: videoUrl,
      vQuality: '720',
      vFormat: 'mp4',
      isAudioOnly: false
    }, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 30000
    })

    if (response.data && response.data.url) {
      await message.send('✅ *Method 2 successful! Sending video...*')
      return await message.sendFromUrl(response.data.url, {
        quoted: message.data,
        caption: `🎬 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n🔧 Method: Cobalt API`
      })
    }
  } catch (error) {
    await message.send(`❌ *Method 2 failed:* ${error.message}`)
  }

  // Method 3: Try SnapSave API
  try {
    await message.send('🔄 *Trying Method 3: SnapSave API...*')

    const snapSaveUrl = `https://snapsave.app/action.php?lang=en`
    const response = await axios.post(snapSaveUrl, `url=${encodeURIComponent(videoUrl)}`, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://snapsave.app/'
      },
      timeout: 30000
    })

    if (response.data && typeof response.data === 'string') {
      // Parse the response to extract download links
      const linkMatch = response.data.match(/href="([^"]*\.mp4[^"]*)"/)
      if (linkMatch && linkMatch[1]) {
        await message.send('✅ *Method 3 successful! Sending video...*')
        return await message.sendFromUrl(linkMatch[1], {
          quoted: message.data,
          caption: `🎬 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n🔧 Method: SnapSave`
        })
      }
    }
  } catch (error) {
    await message.send(`❌ *Method 3 failed:* ${error.message}`)
  }

  // Method 4: Try Y2Mate.com API
  try {
    await message.send('🔄 *Trying Method 4: Y2Mate.com API...*')

    const y2mateUrl = `https://www.y2mate.com/mates/en68/analyze/ajax`
    const response = await axios.post(y2mateUrl, `url=${encodeURIComponent(videoUrl)}&q_auto=1&ajax=1`, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://www.y2mate.com/'
      },
      timeout: 30000
    })

    if (response.data && response.data.result) {
      // Parse the HTML response to find download links
      const linkMatch = response.data.result.match(/k__id="([^"]*)"[^>]*>.*?(\d+p)/)
      if (linkMatch && linkMatch[1]) {
        const convertResponse = await axios.post('https://www.y2mate.com/mates/en68/convert',
          `vid=${videoId}&k=${linkMatch[1]}`, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        })

        if (convertResponse.data && convertResponse.data.dlink) {
          await message.send('✅ *Method 4 successful! Sending video...*')
          return await message.sendFromUrl(convertResponse.data.dlink, {
            quoted: message.data,
            caption: `🎬 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n🔧 Method: Y2Mate.com`
          })
        }
      }
    }
  } catch (error) {
    await message.send(`❌ *Method 4 failed:* ${error.message}`)
  }

  // Method 5: Try KeepVid API
  try {
    await message.send('🔄 *Trying Method 5: KeepVid API...*')

    const keepVidUrl = `https://keepvid.com/download`
    const response = await axios.post(keepVidUrl, `url=${encodeURIComponent(videoUrl)}`, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://keepvid.com/'
      },
      timeout: 30000
    })

    if (response.data && typeof response.data === 'string') {
      // Parse response for download links
      const linkMatch = response.data.match(/href="([^"]*\.mp4[^"]*)"/)
      if (linkMatch && linkMatch[1]) {
        await message.send('✅ *Method 5 successful! Sending video...*')
        return await message.sendFromUrl(linkMatch[1], {
          quoted: message.data,
          caption: `🎬 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n🔧 Method: KeepVid`
        })
      }
    }
  } catch (error) {
    await message.send(`❌ *Method 5 failed:* ${error.message}`)
  }

  // Method 6: Try direct YouTube extraction
  try {
    await message.send('🔄 *Trying Method 6: Direct YouTube extraction...*')

    // Try to get video info directly from YouTube
    const ytResponse = await axios.get(`https://www.youtube.com/watch?v=${videoId}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 30000
    })

    if (ytResponse.data) {
      // Look for video URLs in the page source
      const urlMatch = ytResponse.data.match(/"url":"([^"]*\.mp4[^"]*)"/)
      if (urlMatch && urlMatch[1]) {
        const videoUrl = decodeURIComponent(urlMatch[1].replace(/\\u0026/g, '&'))
        await message.send('✅ *Method 6 successful! Sending video...*')
        return await message.sendFromUrl(videoUrl, {
          quoted: message.data,
          caption: `🎬 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n🔧 Method: Direct extraction`
        })
      }
    }
  } catch (error) {
    await message.send(`❌ *Method 6 failed:* ${error.message}`)
  }

  // Method 7: Try with YouTube cookie if available
  if (YOUTUBE_COOKIE) {
    try {
      await message.send('🔄 *Trying Method 7: YouTube with Cookie...*')

      const cookieApiUrl = `https://api.cobalt.tools/api/json`
      const response = await axios.post(cookieApiUrl, {
        url: videoUrl,
        vQuality: '720',
        vFormat: 'mp4',
        isAudioOnly: false
      }, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Cookie': YOUTUBE_COOKIE,
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        timeout: 30000
      })

      if (response.data && response.data.url) {
        await message.send('✅ *Method 7 successful! Sending video...*')
        return await message.sendFromUrl(response.data.url, {
          quoted: message.data,
          caption: `🎬 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n🔧 Method: Cookie Enhanced`
        })
      }
    } catch (error) {
      await message.send(`❌ *Method 7 failed:* ${error.message}`)
    }
  }

  // If all methods fail, provide helpful message
  return await message.send(
    `❌ *All download methods failed*\n\n` +
    `🔧 *What you can try:*\n` +
    `• Wait a few minutes and try again\n` +
    `• Use \`.ytmp3 ${videoUrl}\` for audio only\n` +
    `• Use \`.ytv ${videoUrl}\` for Y2mate method\n` +
    `• Check if video is available in your region\n` +
    `• Try with a different YouTube video\n\n` +
    `📋 *Video Info:*\n` +
    `🎬 ${safeTitle}\n` +
    `👤 ${safeAuthor}\n` +
    `⏱️ ${safeDuration}\n` +
    `🔗 ${videoUrl}\n\n` +
    `💡 *Note:* YouTube frequently changes their API, causing temporary issues. The bot will be updated to fix any persistent problems.`,
    { quoted: message.data }
  )
}

// Enhanced YouTube Audio Downloader
bot(
  {
    pattern: 'ytmp3 ?(.*)',
    desc: 'Enhanced YouTube audio downloader',
    type: 'download',
  },
  async (message, match) => {
    match = match || message.reply_message.text
    if (!match) return await message.send('*🎵 Enhanced YouTube Audio Downloader*\n\n*Usage:*\n• `.ytmp3 <YouTube URL>`\n• `.ytmp3 <search term>`\n• `.ytmp3 <search term> auto` - Auto download first result')

    const vid = ytIdRegex.exec(match)
    
    // If not a direct URL, search first
    if (!vid) {
      try {
        const result = await yts(match, false, null, message.id)
        if (!result.length) return await message.send(`❌ *No results found for:* ${match}`)
        
        // Check if user wants auto-download
        if (match.includes(' auto') || match.includes(' first') || match.includes(' download')) {
          const searchTerm = match.replace(/ (auto|first|download)/g, '').trim()
          const topResult = result[0]
          
          await message.send(
            `🎯 *Auto-downloading first audio result for:* ${searchTerm}\n\n` +
            `🎵 *${topResult.title}*\n` +
            `👤 ${topResult.author}\n` +
            `⏱️ ${topResult.duration}\n\n` +
            `🔄 *Starting download...*`,
            { quoted: message.data }
          )
          
          // Continue with audio download
          return await downloadAudio(message, topResult.id, topResult)
        } else {
          const msg = generateList(
            result.slice(0, 10).map(({ title, id, duration, view, author }) => ({
              text: `🎵 ${title}\n⏱️ ${duration} | 👁️ ${view}\n👤 ${author}\n`,
              id: `ytmp3 https://www.youtube.com/watch?v=${id}`,
            })),
            `🔍 *Search Results for:* ${match}\n\nSelect a song to download:`,
            message.jid,
            message.participant,
            message.id
          )
          return await message.send(msg.message, { quoted: message.data }, msg.type)
        }
      } catch (error) {
        return await message.send(`❌ *Search failed:* ${error.message}`)
      }
    }

    const videoId = vid[1]
    
    // Get video info
    let videoInfo
    try {
      const [info] = await yts(videoId, true, null, message.id)
      videoInfo = info
    } catch (error) {
      return await message.send(`❌ *Failed to get video info:* ${error.message}`)
    }

    return await downloadAudio(message, videoId, videoInfo)
  }
)

// Function to handle audio download
async function downloadAudio(message, videoId, videoInfo) {
  const { title, duration, author } = videoInfo
  const videoUrl = `https://www.youtube.com/watch?v=${videoId}`

  // Safely handle undefined values
  const safeTitle = title || 'Unknown Title'
  const safeDuration = duration || 'Unknown'
  const safeAuthor = author || 'Unknown Channel'

  await message.send(
    `🎵 *Downloading Audio...*\n\n` +
    `🎵 *Title:* ${safeTitle}\n` +
    `⏱️ *Duration:* ${safeDuration}\n` +
    `👤 *Author:* ${safeAuthor}\n\n` +
    `🔄 *Processing... Please wait*`,
    { quoted: message.data }
  )

  // Method 1: Try Y2mate for audio
  try {
    await message.send('🔄 *Trying Method 1: Y2mate Audio...*')
    const audio = await y2mate.get(videoId, 'audio')

    if (isUrl(audio)) {
      await message.send('✅ *Method 1 successful! Sending audio...*')
      return await message.sendFromUrl(audio, {
        quoted: message.data,
        mimetype: 'audio/mpeg',
        fileName: `${safeTitle}.mp3`
      })
    }

    const result = await y2mate.dl(videoId, 'audio')
    if (result) {
      const { buffer } = await getBuffer(result)
      if (buffer) {
        await message.send('✅ *Method 1 successful! Sending audio...*')
        return await message.send(
          buffer,
          {
            quoted: message.data,
            mimetype: 'audio/mpeg',
            fileName: `${safeTitle}.mp3`
          },
          'audio'
        )
      }
      await message.send('✅ *Method 1 successful! Sending audio...*')
      return await message.sendFromUrl(result, {
        quoted: message.data,
        mimetype: 'audio/mpeg',
        fileName: `${safeTitle}.mp3`
      })
    }
  } catch (error) {
    await message.send(`❌ *Method 1 failed:* ${error.message}`)
  }

  // Method 2: Try Cobalt API for audio
  try {
    await message.send('🔄 *Trying Method 2: Cobalt Audio API...*')

    const apiUrl = `https://api.cobalt.tools/api/json`
    const response = await axios.post(apiUrl, {
      url: videoUrl,
      vQuality: '720',
      vFormat: 'mp3',
      isAudioOnly: true,
      isNoTTWatermark: false,
      isTTFullAudio: false
    }, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 30000
    })

    if (response.data && response.data.url) {
      await message.send('✅ *Method 2 successful! Sending audio...*')
      return await message.sendFromUrl(response.data.url, {
        quoted: message.data,
        mimetype: 'audio/mpeg',
        fileName: `${safeTitle}.mp3`
      })
    }
  } catch (error) {
    await message.send(`❌ *Method 2 failed:* ${error.message}`)
  }

  // Method 3: Try alternative audio API
  try {
    await message.send('🔄 *Trying Method 3: Alternative Audio API...*')

    const audioApiUrl = `https://api.vevioz.com/api/button/mp3/128?url=${encodeURIComponent(videoUrl)}`
    const response = await axios.get(audioApiUrl, {
      timeout: 30000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })

    if (response.data && response.data.url) {
      await message.send('✅ *Method 3 successful! Sending audio...*')
      return await message.sendFromUrl(response.data.url, {
        quoted: message.data,
        mimetype: 'audio/mpeg',
        fileName: `${safeTitle}.mp3`
      })
    }
  } catch (error) {
    await message.send(`❌ *Method 3 failed:* ${error.message}`)
  }

  // If all methods fail
  return await message.send(
    `❌ *All audio download methods failed*\n\n` +
    `🔧 *What you can try:*\n` +
    `• Wait a few minutes and try again\n` +
    `• Use \`.yta ${videoUrl}\` for Y2mate audio\n` +
    `• Use \`.song ${safeTitle}\` to search and download\n` +
    `• Try with a different YouTube video\n\n` +
    `📋 *Audio Info:*\n` +
    `🎵 ${safeTitle}\n` +
    `👤 ${safeAuthor}\n` +
    `⏱️ ${safeDuration}\n` +
    `🔗 ${videoUrl}`,
    { quoted: message.data }
  )
}
