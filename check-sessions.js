#!/usr/bin/env node

/**
 * 🔍 Session Health Monitor
 * 
 * This script checks the health of your dual sessions and detects logout issues
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 Levanter Session Health Monitor')
console.log('==================================\n')

// Load session configurations
const configPath = path.join(__dirname, 'config.json')
if (!fs.existsSync(configPath)) {
  console.error('❌ config.json not found')
  process.exit(1)
}

const sessionConfigs = JSON.parse(fs.readFileSync(configPath, 'utf8'))

// Get valid sessions
const validSessions = Object.keys(sessionConfigs).filter(sessionName => {
  const config = sessionConfigs[sessionName]
  return config.SESSION_ID && 
         config.SESSION_ID !== 'levanter_sessionid' && 
         config.SESSION_ID.length > 20
})

console.log(`📋 Monitoring ${validSessions.length} session(s): ${validSessions.join(', ')}\n`)

// Check each session
validSessions.forEach(sessionName => {
  console.log(`🔍 Checking ${sessionName}:`)
  
  const sessionConfig = sessionConfigs[sessionName]
  const authDir = path.join(__dirname, 'auth', sessionName)
  const logDir = path.join(__dirname, 'logs', sessionName)
  const tempDir = path.join(__dirname, 'temp', sessionName)
  const dbFile = path.join(__dirname, `database_${sessionName}.db`)
  
  // Check SESSION_ID
  if (sessionConfig.SESSION_ID && sessionConfig.SESSION_ID.length > 20) {
    console.log(`   ✅ SESSION_ID: Valid (${sessionConfig.SESSION_ID.substring(0, 20)}...)`)
  } else {
    console.log(`   ❌ SESSION_ID: Invalid or missing`)
  }
  
  // Check auth directory
  if (fs.existsSync(authDir)) {
    const authFiles = fs.readdirSync(authDir)
    console.log(`   ✅ Auth directory: ${authFiles.length} files`)
    
    // Check for session files
    const hasSession = authFiles.some(file => 
      file.includes('session') || 
      file.includes('creds') || 
      file.includes('keys')
    )
    console.log(`   ${hasSession ? '✅' : '❌'} Session files: ${hasSession ? 'Present' : 'Missing'}`)
  } else {
    console.log(`   ❌ Auth directory: Missing`)
  }
  
  // Check database
  if (fs.existsSync(dbFile)) {
    const stats = fs.statSync(dbFile)
    const size = (stats.size / 1024).toFixed(2)
    console.log(`   ✅ Database: ${size} KB`)
  } else {
    console.log(`   ⚠️ Database: Not created yet`)
  }
  
  // Check logs
  if (fs.existsSync(logDir)) {
    const logFiles = fs.readdirSync(logDir)
    console.log(`   ✅ Log directory: ${logFiles.length} files`)
    
    // Check recent activity
    const outputLog = path.join(logDir, 'output.log')
    if (fs.existsSync(outputLog)) {
      const stats = fs.statSync(outputLog)
      const lastModified = Date.now() - stats.mtime.getTime()
      const isRecent = lastModified < 300000 // 5 minutes
      const timeAgo = Math.round(lastModified / 1000)
      console.log(`   ${isRecent ? '✅' : '⚠️'} Recent activity: ${timeAgo}s ago`)
      
      // Check for logout messages in logs
      try {
        const logContent = fs.readFileSync(outputLog, 'utf8')
        const hasLogout = logContent.toLowerCase().includes('logout') || 
                         logContent.toLowerCase().includes('disconnected') ||
                         logContent.toLowerCase().includes('connection closed')
        
        if (hasLogout) {
          console.log(`   ⚠️ Logout detected: Check logs for details`)
        } else {
          console.log(`   ✅ No logout issues detected`)
        }
      } catch (error) {
        console.log(`   ⚠️ Could not read log file`)
      }
    }
  } else {
    console.log(`   ⚠️ Log directory: Missing`)
  }
  
  // Check temp directory
  if (fs.existsSync(tempDir)) {
    console.log(`   ✅ Temp directory: Present`)
  } else {
    console.log(`   ⚠️ Temp directory: Missing`)
  }
  
  console.log('')
})

// Check config.env for conflicts
console.log('🔍 Checking config.env:')
const configEnvPath = path.join(__dirname, 'config.env')
if (fs.existsSync(configEnvPath)) {
  const configEnv = fs.readFileSync(configEnvPath, 'utf8')
  
  // Check for multiple SESSION_IDs (the main cause of logout issues)
  if (configEnv.includes('SESSION_ID') && configEnv.includes(',')) {
    console.log(`   ❌ Multiple SESSION_IDs detected (this causes conflicts!)`)
    console.log(`   💡 Fix: Remove comma-separated SESSION_IDs from config.env`)
  } else {
    console.log(`   ✅ config.env looks good`)
  }
  
  // Check for proper formatting
  const lines = configEnv.split('\n')
  const sessionIdLine = lines.find(line => line.includes('SESSION_ID'))
  if (sessionIdLine) {
    console.log(`   ✅ SESSION_ID format: ${sessionIdLine.substring(0, 50)}...`)
  }
} else {
  console.log(`   ⚠️ config.env not found`)
}

console.log('\n💡 Recommendations:')
console.log('   • Start sessions with: node start-dual-sessions.js')
console.log('   • Use 45+ second delays between session startups')
console.log('   • Keep separate auth directories for each session')
console.log('   • Monitor logs for "logout" or "disconnected" messages')
console.log('   • Restart sessions one at a time if issues occur')

console.log('\n📊 Session Status Summary:')
validSessions.forEach(sessionName => {
  const authExists = fs.existsSync(path.join(__dirname, 'auth', sessionName))
  const dbExists = fs.existsSync(path.join(__dirname, `database_${sessionName}.db`))
  const status = authExists && dbExists ? '🟢 Ready' : '🟡 Setup needed'
  console.log(`   ${sessionName}: ${status}`)
})

console.log('\n✅ Health check complete!')
