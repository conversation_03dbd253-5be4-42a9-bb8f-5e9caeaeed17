{"extra": {"init_session": "[{0}] Инициируется новая сессия", "load_session": "[{0}] Проверка статуса сессии", "invalid_session": "[{0}] {1} НЕДЕЙСТВИТЕЛЬНЫЙ ID СЕССИИ, СКАНИРУЙТЕ СНОВА!!!", "success_session": "[{0}] Сессия успешно подтверждена.", "connecting": "[{0}] Подключение...", "connected": "[{0}] Подключено {1}", "instance_close": "[{0}] Соединение закрыто", "instance_restart": "[{0}] Перезапуск экземпляра...", "reconnect": "[{0}] Переподключение...({1})", "reconnect_after": "[{0}] Переподключение через 1 минуту", "another_login": "[{0}] Сессия вошла в систему на другом устройстве.", "error_message": "```---ОТЧЕТ ОБ ОШИБКЕ---\n\nВерсия : {0}\nСообщение : {1}\nОшибка   : {2}\nJid     : {3}\nКоманда : {4}\nПлатформа : {5}```\n\n```-----``` *Создано LyFE с ❣* ```-----```", "deleted_message": "           удаленноеСообщение", "install_external_plugin": "[{0}] Установка внешних плагинов...", "installed_external_plugin": "[{0}] Внешние плагины установлены", "plugin_install": "[{0}] Установка плагинов...", "plugin_installed": "[{0}] Установлено {1}", "plugins_installed": "[{0}] Плагины установлены", "plugin_install_error": "[{0}] Ош<PERSON>б<PERSON>а установки {1}, удаление плагина", "plugin_not_found": "[{0}] плагин {1} не найден (404), удаление плагина", "group_cmd": "Эта команда доступна только в групповых чатах."}, "plugins": {"common": {"reply_to_message": "Ответьте на сообщение", "not_admin": "_Я не администратор._", "reply_to_image": "_Ответьте на изображение_", "update": "_Настройки успешно обновлены! Ваши предпочтения сохранены и теперь активны._"}, "menu": {"help": "```╭────────────────╮\n      ʟᴇᴠᴀɴᴛᴇʀ\n╰────────────────╯\n\n╭────────────────\n│ Префикс : {0}\n│ Пользователь : {1}\n│ Время : {2}\n│ День : {3}\n│ Дата : {4}\n│ Версия : {5}\n│ Плагины : {6}\n│ ОЗУ : {7}\n│ Время работы : {8}\n│ Платформа : {9}\n╰────────────────```", "menu": "```╭═══ LEVANTER ═══⊷\n┃❃╭──────────────\n┃❃│ Префикс : {0}\n┃❃│ Пользователь : {1}\n┃❃│ Время : {2}\n┃❃│ День : {3}\n┃❃│ Дата : {4}\n┃❃│ Версия : {5}\n┃❃│ Плагины : {6}\n┃❃│ ОЗУ : {7}\n┃❃│ Время работы : {8}\n┃❃│ Платформа : {9}\n┃❃╰───────────────\n╰═════════════════⊷```"}, "afk": {"example": "> *Использование AFK:*\n- Установить AFK: *afk [причина]*\n- Пример: *afk Я занят* Последний раз видели #lastseen назад\n- Отправка сообщения автоматически снимает статус AFK\n- Отключить AFK: *afk off*", "not_afk": "_Вы больше не в режиме AFK._", "desc": "_Установить статус AFK (Отошел от клавиатуры)_"}, "alive": {"default": "_Я жив\nВремя работы : #uptime_", "desc": "_Отобразить сообщение о статусе активности бота с необязательным пользовательским текстом._"}, "antifake": {"example": "*Статус Antifake:* {0}\n\n> *Примеры использования:*\n- *antifake list* - Просмотреть коды стран\n- *antifake !91,1* - Разрешить/Запретить определенные коды стран\n- *antifake on | off* - Включить/Отключить antifake", "desc": "_Включить или настроить защиту от фальшивых номеров_", "not": "_Нет кодов стран для отображения._", "status": "_Antifake теперь *{0}*._", "update": "> Antifake обновлен\n*Разрешено:* {0}\n*Запрещено:* {1}"}, "antilink": {"desc": "_Включить или настроить защиту от ссылок_", "disable": "_Antilink уже отключен._", "antilink_notset": "_Antilink не настроен._", "status": "_Antilink теперь *{0}*._", "info": "> Статус Antilink: {0}\n*Разрешенные URL:* {1}\n *Действие :* {2}", "action_invalid": "*Указано недействительное действие.*", "action_update": "_Действие Antilink обновлено до: *{0}*_", "update": "> Antilink обновлен\n*Разрешено:* {0}\n*Запрещено:* {1}", "example": "Статус Antilink: *{0}*\n\n> Примеры использования:\n- antilink info - Просмотреть текущие настройки\n- antilink whatsapp.com - Разрешить определенные URL\n- antilink on | off - Включить/Отключить antilink\n- antilink action/<kick | warn | null> - Установить действие для ссылки"}, "antiword": {"desc": "_Фильтровать определенные слова в групповом чате_", "example": "Статус AntiWord: {0}\n> *Примеры использования:*\n- antiword action/<kick | warn | null>* - Установить действие для нарушений\n- antiword on | off - Включить/Отключить фильтрацию слов\n- setvar ANTIWORDS:слово1,слово2,... - Определить запрещенные слова", "action_update": "Действие AntiWord обновлено до: *{0}*", "status": "_AntiWord теперь *{0}*._"}, "apk": {"desc": "Скачать APK с APKMirror", "example": "> *Примеры использования:*\n- apk Mixplorer\n- apk whatsapp,apkm (включает APK-бандлы)", "no_result": "_Результатов по вашему запросу не найдено._", "apps_list": "Соответствующие приложения ({0})"}, "delete": {"desc": "Антиудаление: Восстановить удаленные сообщения", "example": "> *Примеры использования:*\n- delete p - Отправить удаленные сообщения в ваш чат/суперпользователю\n- delete g - Отправить удаленные сообщения в ту же группу\n- delete off - Отключить антиудаление\n- delete <jid> - Отправить удаленные сообщения на определенный JID", "invalid_jid": "*Ошибка:* _Недействительный JID_", "dlt_msg_jid": " _Удаленные сообщения будут отправлены на: {0}_", "dlt_msg_disable": "Антиудаление отключено.", "dlt_msg_sudo": "_Удаленные сообщения будут отправлены в ваш чат или суперпользователю._", "dlt_msg_chat": "_Удаленные сообщения будут отправлены в сам чат._"}, "dlt": {"desc": "Удалить отвеченные сообщения"}, "fb": {"desc": "Скачать видео с Facebook", "example": "", "quality": "Выберите качество видео", "invalid": "*Ошибка:* _Видео по указанному URL не найдено._"}, "fancy": {"desc": "Создает декоративный текст из заданного текста", "example": "> *Использование:*\nfancy <текст>\nfancy <номер_шрифта> (ответ на сообщение)\n\n*Пример:*\n- fancy Привет\n- fancy 7 (при ответе на сообщение)", "invalid": "*Недействительный номер шрифта!*\nПожалуйста, введите число от *1 до 47*."}, "stop": {"desc": "Удалить фильтры в чате", "example": "> *Использование:*\n- stop <фильтр>\n- stop привет", "delete": "{0} удален", "not_found": "_{0} не найден в фильтрах._"}, "filter": {"desc": "Управлять фильтрами в группах", "example": "> *Пример:*\n- filter привет (при ответе на текстовое сообщение)\n- filter list (Показывает текущие фильтры)", "list": "> *Текущие фильтры:*\n{0}", "filter_add": "*{0}* фильтр добавлен"}, "forward": {"desc": "Переслать отвеченное сообщение на указанный(е) JID(ы)", "foward": "Сообщение переслано на: {0}", "example": "Недействительный JID!\n> *Использование:*\n- forward <jid>\n- forward <EMAIL>"}, "save": {"desc": "Переслать отвеченное сообщение себе", "save": "Сообщение сохранено!"}, "gemini": {"desc": "Google Gemini AI - Спрашивайте что угодно!", "example": "> *Пример:*\n- gemini привет\n- gemini что на картинке (при ответе на изображение)", "Key": "> Отсутствует ключ API Gemini!\nПолучите его на: https://aistudio.google.com/app/apikey\n\n*Установите его с помощью:*\nsetvar GEMINI_API_KEY = ваш_ключ_api"}, "gstop": {"desc": "Удалить глобальные фильтры во всех группах", "example": "> *Использование:*\n- gstop <фильтр>\n- gstop привет", "delete": "{0} удален", "not_found": "_{0} не найден в глобальных фильтрах._"}, "pstop": {"desc": "Удалить персональные фильтры во всех группах", "example": "> *Использование:*\n- pstop <фильтр>\n- pstop привет", "delete": "{0} удален", "not_found": "_{0} не найден в персональных фильтрах._"}, "gfilter": {"desc": "Управлять глобальными фильтрами в группах", "example": "> *Пример:*\n- gfilter привет (при ответе на текстовое сообщение)\n- gfilter list (Показывает текущие глобальные фильтры)", "add": "*{0}* глобальный фильтр добавлен"}, "pfilter": {"desc": "Управлять глобальными фильтрами в личных чатах", "example": "> *Пример:*\n- pfilter привет (при ответе на текстовое сообщение)\n- pfilter list (Показывает текущие персональные фильтры)", "add": "*{0}* персональный фильтр добавлен"}, "gpp": {"desc": "Изменить иконку группы", "update": "_Иконка группы обновлена_"}, "greet": {"setdesc": "Установить персонализированное приветственное сообщение", "setexample": "> *Пример:* setg<PERSON> Привет, это бот. Мой хозяин скоро ответит.", "setupdate": "_Приветственное сообщение обновлено._", "getdesc": "Получить персонализированное приветственное сообщение", "notsetgreet": "> Приветственное сообщение не установлено.", "deldesc": "Удалить персонализированное приветственное сообщение", "delupdate": "Приветственное сообщение удалено."}, "greetings": {"welcome_desc": "Отправить приветственное сообщение новым участникам", "welcome_example": "Приветствие сейчас {0}\n\nДля подробностей посетите: https://levanter-plugins.vercel.app/faq", "welcome_enable": "_Приветствие теперь включено_", "welcome_disable": "_Приветствие теперь отключено_", "welcome_delete": "_Приветственное сообщение удалено_", "goodbye_desc": "Отправить прощальное сообщение участникам", "goodbye_example": "Прощание сейчас {0}\n\nДля подробностей посетите: https://levanter-plugins.vercel.app/faq", "goodbye_enable": "_Прощание теперь включено_", "goodbye_disable": "_Прощание теперь отключено_", "goodbye_delete": "_Прощальное сообщение удалено_"}, "groq": {"example": "*Пример:* groq Привет\n\nВы можете дополнительно установить следующие переменные окружения:\n- GROQ_API_KEY\n- GROQ_MODEL\n- GROQ_SYSTEM_MSG\n\nДля подробностей посетите: https://console.groq.com/keys", "desc": "Взаимодействовать с GROQ AI"}, "kick": {"desc": "Удалить участников из группы.", "not_admin": "Я не администратор, поэтому не могу удалять участников.", "mention_user": "Пожалуйста, упомяните пользователя или ответьте на его сообщение.", "admin": "Указанный пользователь является администратором и не может быть удален.", "kicking_all": "Удаление всех участников, не являющихся администраторами... ({0} участников). Перезапустите бота, если хотите остановить."}, "add": {"desc": "Добавить участника в группы", "warning": "> Избегайте добавления номеров, не сохраненных в контактах; это может увеличить риск блокировки.", "not_admin": "Я не администратор, поэтому не могу добавлять участников.", "invalid_number": "Пожалуйста, укажите действительный номер телефона. Пример: add 91987654321", "failed": "Не удалось добавить. Вместо этого отправлено приглашение."}, "promote": {"desc": "Дать роль администратора", "not_admin": "Я не администратор, поэтому не могу изменять роли.", "mention_user": "Пожалуйста, упомяните пользователя или ответьте на его сообщение.", "already_admin": "Пользователь уже является администратором."}, "demote": {"desc": "Снять роль администратора", "not_admin": "Я не администратор, поэтому не могу изменять роли.", "mention_user": "Пожалуйста, упомяните пользователя или ответьте на его сообщение.", "not_admin_user": "Пользователь не является администратором."}, "invite": {"desc": "Получить ссылку-приглашение в группу", "not_admin": "Я не администратор, поэтому не могу создать ссылку-приглашение.", "success": "Вот ссылка-приглашение в группу:\n{0}"}, "mute": {"desc": "Сделать группу только для администраторов", "not_admin": "Я не администратор, поэтому не могу изменять настройки группы.", "mute": "Заглушено на {0} минут."}, "unmute": {"desc": "Сделать группу доступной для всех участников для отправки сообщений", "not_admin": "Я не администратор, поэтому не могу изменять настройки группы."}, "join": {"desc": "Присоединиться к группам по ссылке-приглашению", "invalid_link": "Пожалуйста, предоставьте действительную ссылку-приглашение WhatsApp.", "group_full": "Группа заполнена и не может принять новых участников.", "success": "Успешно присоединился к группе.", "request_sent": "Запрос на присоединение отправлен."}, "revoke": {"desc": "Отозвать ссылку-приглашение группы", "not_admin": "Я не администратор, поэтому не могу отозвать ссылку-приглашение."}, "group_info": {"desc": "Показать информацию о ссылке-приглашении группы", "invalid_link": "Пожалуйста, предоставьте действительную ссылку-приглашение WhatsApp.", "details": "*Название:* {0}\n*ID группы:* {1}@g.us\n*Владелец:* {2}\n*Участники:* {3}\n*Создано:* {4}\n*Описание:* {5}"}, "common_members": {"desc": "Показать или удалить общих участников в двух или более группах", "found": "Найдено 0 общих участников."}, "insta": {"usage": "Пример: insta <URL Instagram>", "not_found": "Не найдено.", "desc": "Скачать посты, рилсы и видео из Instagram."}, "ison": {"usage": "Пример: ison <номер телефона>", "not_exist": "`*Не существует в WhatsApp* ({0})\n`", "exist": "\n*Существует в WhatsApp* ({0})\n", "privacy": "*Настройки конфиденциальности включены* ({0})\n", "desc": "Проверить, зарегистрирован ли номер телефона в WhatsApp."}, "lydia": {"usage": "Использование: lydia on | off\nОтветьте или упомяните, чтобы активировать для конкретного пользователя.", "activated": "<PERSON> активир<PERSON>ана.", "deactivated": "<PERSON> деактив<PERSON><PERSON><PERSON><PERSON><PERSON>.", "note": "Это работает только из ответного сообщения.", "desc": "Включить или отключить функцию чат-бота."}, "rotate": {"usage": "Пример: rotate right|left|flip (ответьте на видео).", "not_found": "Пожалуйста, ответьте на видео и укажите правильное направление поворота (right, left или flip).", "desc": "Повернуть видео вправо, влево или перевернуть его.", "convert": "_Конвертация..._"}, "mp3": {"usage": "Ответьте на видео или аудио, чтобы конвертировать в MP3.", "not_found": "Пожалуйста, ответьте на сообщение с видео или аудио.", "desc": "Конвертировать видео в аудио или аудиоклип в голосовую заметку."}, "photo": {"usage": "Ответьте на стикер с фото, чтобы конвертировать его в изображение.", "desc": "Конвертировать стикер в изображение."}, "reverse": {"usage": "Ответьте на видео или аудио, чтобы развернуть воспроизведение.", "not_found": "Пожалуйста, ответьте на сообщение с видео или аудио.", "desc": "Развернуть воспроизведение видео или аудиоклипа."}, "cut": {"usage": "Пример: cut 0;30 (нача<PERSON><PERSON>;длительность) (ответьте на видео или аудио).", "not_found": "Пожалуйста, ответьте на видео или аудио с действительными значениями начала и длительности (например, 10;30).", "desc": "Вырезать сегмент из аудио или видеофайла."}, "trim": {"usage": "Пример: trim 10;30 (ответьте на видео).", "not_found": "Пожалуйста, ответьте на видео с действительными значениями начала и длительности (например, 60;30).", "desc": "Обрезать видео между указанными временем начала и длительностью."}, "page": {"usage": "Пример: page 1 (ответьте на изображение).", "not_found": "Пожалуйста, ответьте на изображение с числовой подписью, указывающей номер страницы.", "desc": "Добавить изображение как страницу в PDF-документ.", "add": "Страни<PERSON>а {0} добавлена!"}, "pdf": {"usage": "Пример: pdf заметка (укажите заголовок для PDF).", "not_found": "Пожалуйста, укажите заголовок для PDF-документа.", "desc": "Конвертировать изображения в PDF-документ."}, "merge": {"usage": "Пример: merge 1 (ответьте с номером порядка на видео).", "not_found": "Пожалуйста, ответьте на видео с действительным номером порядка.", "desc": "Объединить несколько видео в одно.", "merge": "_Объединение {0} видео_", "add": "_Видео {0} добавлено_"}, "compress": {"usage": "Ответьте на видео, чтобы сжать его.", "desc": "Сжать видеофайл для уменьшения его размера."}, "bass": {"usage": "Пример: bass 10 (ответьте на аудио или видео).", "desc": "Изменить уровень басов в аудиофайле."}, "treble": {"usage": "Пример: treble 10 (ответьте на аудио или видео).", "desc": "Изменить уровень высоких частот в аудиофайле."}, "histo": {"usage": "Ответьте на аудио или видео, чтобы создать видео с гистограммой.", "desc": "Конвертировать аудио в визуальное видео с гистограммой."}, "vector": {"usage": "Ответьте на аудио или видео, чтобы создать видео с векторной визуализацией.", "desc": "Конвертировать аудио в видео с векторной визуализацией."}, "crop": {"usage": "Пример: crop 512,512,0,512 (ответьте на видео).", "not_found": "Пожалуйста, ответьте на видео с действительными размерами обрезки в формате: out_w,out_h,x,y.", "desc": "Обрезать видео до указанных размеров.", "xcrop": "Ширина видео: *{0}*, высота: *{1}*\nВыберите выходной размер в пределах этих значений."}, "low": {"usage": "Ответьте на аудио или видео, чтобы понизить тон.", "desc": "Изменить высоту звука на более низкий тон."}, "pitch": {"usage": "Ответьте на аудио или видео, чтобы настроить высоту звука.", "not_found": "Пожалуйста, ответьте на сообщение с аудио или видео.", "desc": "Настроить высоту звука в аудиофайле."}, "avec": {"usage": "Ответьте на аудио или видео, чтобы конвертировать его в видеоформат.", "not_found": "Пожалуйста, ответьте на сообщение с аудио или видео.", "desc": "Конвертировать аудиоклип в видео."}, "avm": {"usage": "Ответьте с аудио и видео, чтобы объединить их.", "desc": "Объединить аудио и видеофайлы в один.", "audio_add": "_Аудио добавлено!_", "video_add": "_Видео добавлено!_"}, "black": {"usage": "Ответьте на аудио или видео, чтобы создать видео с черным фоном.", "desc": "Конвертировать аудиоклип в видео с черным фоном."}, "mediafire": {"usage": "Пример: mediafire <URL Mediafire>", "not_found": "Файл не найден. Пожалуйста, проверьте URL и попробуйте снова.", "desc": "Скачать файл с Mediafire."}, "mention": {"usage": "Пример: mention on | off | get\n(Ответьте на сообщение, чтобы настроить для конкретного пользователя.)", "desc": "Настроить и управлять функцией упоминания для автоматических ответов.", "not_activated": "Ответ на упоминание не активирован.", "current_status": "Упоминание сейчас {0}. Для подробностей посетите: https://levanter-plugins.vercel.app/faq", "activated": "Ответ на упоминание активирован.", "deactivated": "Ответ на упоминание деактивирован.", "updated": "Настройки упоминания обновлены."}, "status": {"usage": "Использование: status on | off | no-dl | except-view <jid,...> | only-view <jid,...>", "desc": "Автоматически управлять просмотром статусов WhatsApp."}, "call": {"usage": "Использование: call on | off", "desc": "Автоматически отклонять входящие звонки."}, "read": {"usage": "Использование: read on | off", "desc": "Включить или отключить автоматическое чтение входящих сообщений."}, "online": {"usage": "Использование: online on | off", "desc": "Держа<PERSON>ь статус аккаунта всегда онлайн."}, "movie": {"usage": "Пример: movie <название фильма>", "not_found": "Фильм не найден. Пожалуйста, проверьте название и попробуйте снова.", "desc": "Получить подробную информацию о фильме, включая полный сюжет, из OMDB API."}, "msgs": {"desc": "Отобразить количество сообщений группы для каждого участника, включая индивидуальные итоги и время последнего появления.", "msg_init": "\n*Номер :* {0}\n*Имя :* {1}\n*Всего сообщений :* {2}\n", "msg_last": "*Последний раз видели :* {0} назад\n"}, "reset": {"usage": "Пример: reset all ИЛИ reset <ответ/упоминание>", "desc": "Сбросить счетчик сообщений для всей группы или конкретного участника.", "reset_all": "Счетчики сообщений всех участников удалены", "reset_one": "_Счетчики сообщений @{0} удалены._"}, "inactive": {"usage": "> Примеры:\n- inactive day 10\n- inactive day 10 kick\n- inactive total 100\n- inactive total 100 kick\n- inactive day 7 total 150\n- inactive day 7 total 150 kick\n\nЕсли не указано 'kick', просто вывести список", "desc": "Определить или удалить неактивных участников на основе количества сообщений или длительности неактивности. Добавьте 'kick' для удаления участников.", "inactives": "_Всего неактивных: {0}_", "removing": "_Удаление {0} неактивных участников через 7 секунд_"}, "amute": {"usage": "Использование: amute <час> <минута>\n- amute on | off\n- amute info\n\nОтветьте текстом, чтобы установить сообщение заглушки", "desc": "Запланировать автоматическое заглушение группы в указанное время с необязательным пользовательским сообщением.", "not_found": "Настройки AutoMute не найдены.", "already_disabled": "AutoMute уже отключен.", "enabled": "AutoMute включен.", "disabled": "AutoMute отключен.", "invalid_format": "> Пример:\n- amute 6 0\n- amute on | off\n- amute info\n\nОтветьте текстом, чтобы установить сообщение заглушки.", "scheduled": "Группа будет заглушена в {0}\n*сообщение :* {1}", "info": "Час: {0}\nМинута: {1}\nВремя: {2}\nЗаглушка: {3}\nСообщение: {4}"}, "aunmute": {"usage": "Использование: aunmute <час> <минута>\n- aunmute on | off\n- aunmute info\nОтветьте текстом, чтобы установить сообщение разблокировки", "desc": "Запланировать автоматическую разблокировку группы в указанное время с необязательным пользовательским сообщением.", "not_found": "Настройки AutoUnMute не найдены.", "already_disabled": "AutoUnMute уже отключен.", "enabled": "AutoUnMute включен.", "disabled": "AutoUnMute отключен.", "invalid_format": "> Пример:\n- aunmute 16 30\n- aunmute on | off\n- aunmute info\n\nОтветьте текстом, чтобы установить сообщение разблокировки.", "scheduled": "Группа будет разблокирована в {0}\n*сообщение :* {1}", "info": "Час: {0}\nМинута: {1}\nВремя: {2}\nЗаглушка: {3}\nСообщение: {4}"}, "zushi": {"usage": "> Пример:\n- zushi ping, sticker\n- Чтобы установить все команды, введите 'list', затем ответьте скопированным сообщением (например, zushi скопированное_сообщение).", "desc": "Позволяет включить определенные команды для использования другими в чате.", "already_set": "{0} уже настроен.", "allowed": "*разрешенные команды для* @{0}\n{1}"}, "yami": {"usage": "Просто используйте: yami", "desc": "Отображает список команд, которые в настоящее время разрешены в этом чате.", "not_set": "Разрешенные команды еще не установлены."}, "ope": {"usage": "Пример: ope ping, sticker ИЛИ ope all", "desc": "Удаляет или снимает указанные разрешенные команды.", "not_found": "Разрешенные команды для {0} не найдены.", "all_removed": "Все разрешенные команды удалены.", "removed": "*удаленные команды для* @{0}\n{1}"}, "pdm": {"usage": "Использование: pdm on | off", "desc": "Включить или отключить автоматические уведомления о событиях повышения/понижения в группе.", "not_found": "Пожалуйста, укажите 'on' или 'off'. Например: pdm on", "activated": "Оповещение о повышении/понижении активировано.", "deactivated": "Оповещение о повышении/понижении деактивировано."}, "ping": {"desc": "Проверить время отклика бота (задержку).", "ping_sent": "Пинг!", "pong": "Понг! Время отклика: {0} мс"}, "pinterest": {"usage": "Пример: pinterest <URL Pinterest>", "not_found": "Медиа не найдено. Пожалуйста, проверьте URL и попробуйте снова.", "desc": "Скачать видео или изображения с Pinterest."}, "plugin": {"usage": "> Пример:\n- plugin <URL Gist>\n- plugin list", "desc": "Установить внешние плагины, указав URL Gist с кодом плагина, или вывести список всех установленных плагинов.", "invalid": "Пожалуйста, укажите действительный URL плагина или имя плагина.", "not_installed": "В настоящее время плагины не установлены.", "installed": "Новые установленные плагины: {0}"}, "remove": {"usage": "> Пример:\n- remove <имя_плагина>\n- remove all", "desc": "Удалить внешние плагины, указав имя плагина, или удалить все установленные плагины.", "not_found": "Плагин *{0}* не найден.", "removed": "Плагины успешно удалены."}, "reboot": {"desc": "Перезапустить экземпляр бота с использованием PM2.", "starting": "Перезапуск..."}, "fullpp": {"usage": "Пример: fullpp (ответьте на изображение)", "desc": "Установить полноразмерное изображение профиля.", "updated": "Изображение профиля обновлено."}, "jid": {"desc": "Возвращает JID пользователя или чата. Проверяет упомянутого пользователя, ответное сообщение или использует JID текущего чата по умолчанию."}, "left": {"desc": "Покинуть текущую группу. Если указан дополнительный текст, он будет отправлен перед уходом."}, "block": {"usage": "Пример: block (ответьте или упомяните пользователя)", "desc": "Заблокировать указанного пользователя.", "status": "Заблокирован"}, "unblock": {"usage": "Пример: unblock (ответьте или упомяните пользователя)", "desc": "Разблокировать указанного пользователя.", "status": "_Разблокирован_"}, "pp": {"usage": "Пример: pp (ответьте на изображение)", "desc": "Обновить ваше изображение профиля, используя изображение из отвеченного сообщения."}, "whois": {"number": "*Номер :* {0}", "name": "*Имя :* {0}", "about": "*О себе :* {0}", "setAt": "*Установлено :* {0}", "owner": "*Владелец :* {0}", "members": "*Участники* : {0}", "description": "*описание* : {0}", "created": "*Создано* : {0}", "usage": "Пример: whois <jid или идентификатор пользователя>", "desc": "Отображает изображение профиля и дополнительную информацию (например, о себе, статус) пользователя или группы."}, "gjid": {"desc": "Выводит список всех JID групп вместе с их названиями."}, "qr": {"usage": "Пример: qr тест ИЛИ ответьте на изображение QR с qr", "desc": "Сгенерировать QR-код из указанного текста или декодировать QR-код из отвеченного изображения."}, "reddit": {"usage": "Пример: reddit <URL>", "desc": "Скачивает видео из указанного поста Reddit по предоставленному URL.", "error": "Видео по указанному URL не найдено."}, "rmbg": {"usage": "Пример: rmbg (ответьте на изображение)", "desc": "Удаляет фон из отвеченного изображения с использованием API remove.bg", "key": "Чтобы использовать эту команду, убедитесь, что вы зарегистрировались на remove.bg, подтвердили аккаунт, скопировали ключ API и установили его с помощью .setvar RMBG_KEY:<ваш_ключ_api> (например, .setvar RMBG_KEY:GWQ6jVy9MBpfYF9SnyG8jz8P). РЕГИСТРАЦИЯ: https://accounts.kaleido.ai/users/sign_up | КЛЮЧ API: https://www.remove.bg/dashboard#api-key", "error": "Отсутствует ключ API или изображение. Пожалуйста, установите ключ API и ответьте на изображение."}, "setschedule": {"usage": "> *Пример:*\n- setschedule jid,мин-час-день-месяц (в 24-часовом формате, день и месяц необязательны)\n- setschedule <EMAIL>, 9-9-13-8\n- setschedule <EMAIL>, 0-10 (отправить сообщение ежедневно в 10 утра)\n- setschedule <EMAIL>, 0-10, once (отправить сообщение в 10 утра один раз)", "desc": "Запланировать автоматическую отправку сообщения в указанное время. Укажите целевой JID(ы) и время (в формате минуты-час-день-месяц; день и месяц необязательны). Ответьте на сообщение, которое хотите запланировать.", "invalid": "Недействительный формат расписания или время. Пожалуйста, следуйте одному из предоставленных примеров.", "no_reply": "Пожалуйста, ответьте на сообщение, которое вы хотите запланировать для отправки.", "scheduled": "_Успешно запланировано на отправку в_ *{0}* _в_ @{1}."}, "getschedule": {"desc": "Получить все запланированные сообщения для указанного чата.", "not_found": "Запланированных сообщений нет.", "time": "Время : {0}"}, "delschedule": {"usage": "> Пример:\n- delschedule <EMAIL>, 8-8-10-10\n- delschedule <EMAIL>\n- delschedule all", "desc": "Удалить запланированное сообщение, указав целевой JID и время, или удалить все запланированные сообщения.", "invalid": "Недействительный формат. Пожалуйста, следуйте одному из примеров.", "not_found": "Расписание не найдено.", "deleted": "_Расписание удалено._"}, "setstatus": {"usage": "Пример: setstatus jid,jid,jid,... ИЛИ setstatus contact", "desc": "Установить статус WhatsApp для конкретных контактов или для импортированных контактов. Ответьте на сообщение (текст, изображение или видео), чтобы установить статус.", "reply_required": "Пожалуйста, ответьте на сообщение, чтобы установить статус.", "sent": "_Статус отправлен {0} контактам._"}, "scstatus": {"usage": "Примеры:\n- scstatus jid,jid,jid,...|мин-час-день-месяц (день и месяц необязательны)\n- scstatus contact|мин-час-день-месяц\n- scstatus delete all|мин-час-день-месяц\n- scstatus list", "desc": "Запланировать статус WhatsApp для отправки в указанное время. Используйте 'contact' для импортированных контактов или укажите конкретные JID.", "reply_required": "Пожалуйста, ответьте на сообщение, чтобы запланировать статус.", "invalid": "Недействительный формат. Пожалуйста, следуйте одному из предоставленных примеров.", "scheduled": "_Успешно запланировано на отправку в_ {0}.", "list": "Список запланированных статусов:", "deleted": "Расписание удалено."}, "antispam": {"usage": "Использование: antispam on | off", "desc": "Включить или отключить функцию AntiSpam для группы.", "activated": "AntiSpam активирован.", "deactivated": "AntiSpam деактивирован."}, "sticker": {"desc": "Конвертировать изображение или видео в стикер. Ответьте на сообщение с изображением или видео, чтобы создать стикер.", "reply_required": "Пожалуйста, ответьте на изображение или видео."}, "circle": {"desc": "Конвертировать изображение в круглый стикер.", "reply_required": "Пожалуйста, ответьте на изображение."}, "take": {"usage": "Пример: take <заголовок,исполнители,url> (ответьте на стикер или аудио). Для аудио заголовок обязателен; исполнители и URL необязательны.", "desc": "Изменить пакет стикеров, обновив его метаданные. Если ответ на стикер, обновить метаданные пакета. Если ответ на аудио, добавить метаданные (заголовок, исполнители, URL) к файлу.", "reply_required": "Пожалуйста, ответьте на сообщение со стикером или аудио.", "additional_info": "Для метаданных аудио исполнители или URL необязательны."}, "mp4": {"desc": "Конвертировать анимированный стикер (WebP) в видео MP4.", "reply_required": "Пожалуйста, ответьте на анимированный стикер."}, "story": {"usage": "Пример: story <имя_пользователя> (или ответьте на сообщение, содержащее имя пользователя)", "desc": "Скачать истории Instagram для указанного имени пользователя. Если доступно несколько историй, будет предоставлен список для выбора.", "not_found": "Истории для указанного имени пользователя не найдены.", "list": "Всего доступно {0} историй. Пожалуйста, выберите одну для скачивания.\n"}, "tag": {"usage": "Пример: tag all | tag admin | tag notadmin | tag <пользовательское_сообщение> (или ответьте на сообщение с 'tag')", "desc": "Упомянуть участников группы в зависимости от выбора. Используйте 'all' для упоминания всех участников, 'admin' для упоминания только администраторов группы, 'notadmin' для упоминания участников, не являющихся администраторами, или укажите пользовательское сообщение, которое будет отправлено вместе с упоминаниями."}, "tictactoe": {"usage": "Пример: tictactoe <jid_противника> ИЛИ tictactoe restart <jid_противника> ИЛИ tictactoe end", "desc": "Играть в крестики-нолики против противника. Вызовите пользователя, упомянув его, ответив на его сообщение или указав его JID. Используйте 'tictactoe end' для завершения игры и 'tictactoe restart <jid_противника>' для перезапуска с новым противником.", "choose_opponent": "Пожалуйста, выберите противника, ответив на сообщение или упомянув пользователя. Вы не можете играть против себя.", "game_ended": "Игра завершена.", "game_restarted": "Игра перезапущена с новым противником.", "invalid_input": "Недействительный ввод. Пожалуйста, укажите действительного противника или используйте 'end' или 'restart' соответствующим образом.", "players": "Игроки", "already_occupied": "_Уже занято_", "current_player": "текущийИгрок", "game_finish": "Игра завершена 🏁", "winner": "Победитель"}, "tiktok": {"usage": "Использование: tiktok <URL TikTok> (или ответьте на сообщение с URL)", "desc": "Скачать видео TikTok по предоставленному URL.", "not_found": "Видео не найдено. Пожалуйста, проверьте URL и попробуйте снова."}, "tog": {"usage": "Пример: tog ping off", "desc": "Включить или отключить команду бота.", "invalid": "Недействительный ввод. Используйте: tog <команда> on|off (например, tog ping off)", "self_reference": "Вы действительно хотите меня убить?", "enabled": "{0} Включено.", "disabled": "{0} Отключено."}, "trt": {"usage": "Пример: trt ml привет ИЛИ trt ml (ответьте на текстовое сообщение)", "desc": "Перевести текст с помощью Google Translate. Укажите код целевого языка (и, при необходимости, код исходного языка) при ответе на сообщение."}, "twitter": {"usage": "Пример: twitter <URL Twitter> (или ответьте на сообщение, содержащее URL)", "desc": "Скачать видео Twitter. Если доступно несколько вариантов качества, вам будет предложено выбрать один.", "not_found": "Видео по указанному URL Twitter не найдено.", "choose_quality": "> Выберите качество видео\n"}, "upload": {"usage": "Пример: upload <URL> (или ответьте на сообщение, содержащее URL)", "desc": "Скачать медиа по предоставленному URL. Для укороченных URL Google Images прямой URL изображения извлекается автоматически."}, "url": {"usage": "Пример: url ИЛИ url imgur (ответьте на изображение или видео)", "desc": "Конвертировать изображение или видео в URL. При необходимости укажите параметр (например, 'imgur'), чтобы получить URL от конкретного сервиса."}, "getvar": {"usage": "Пример: get<PERSON> sudo", "desc": "Отобразить значение переменной. Укажите ключ переменной (регистр не учитывается), чтобы получить ее значение.", "not_found": "{0} не найдено в переменных."}, "delvar": {"usage": "Пример: <PERSON><PERSON> sudo", "desc": "Удалить переменную, указав ее ключ.", "not_found": "{0} не найдено в переменных.", "deleted": "{0} удалено."}, "setvar": {"usage": "Пример: set<PERSON> ключ = значение", "desc": "Установить переменную с конкретным ключом и значением. Используйте '=' для разделения ключа и значения.", "success": "Новая переменная {0} добавлена как {1}."}, "allvar": {"desc": "Отобразить все сохраненные переменные в отсортированном порядке."}, "vote": {"usage": "> Пример:\nvote q|Какой ваш любимый цвет?\no|😀|Синий\no|😊|Красный", "desc": "Инициировать голосование в группе WhatsApp.", "notes": "Если получатели не указаны, сообщение о голосовании будет просто отправлено в текущую группу.", "no_vote": "Голосований нет!", "total_vote": "всего голосов : *{0}*", "delete_vote": "Удалите текущее голосование, чтобы установить новое.", "option_required": "требуется два или более варианта", "question_required": "требуется вопрос", "vote": "Реагируйте или отвечайте вариантом для голосования.", "vote_deleted": "_Голосование удалено._", "voted": "@{0} проголосовал за {1}\n\n${2}"}, "warn": {"usage": "Пример: warn @пользователь ИЛИ warn reset @пользователь. Вы также можете ответить на сообщение пользователя и ввести 'warn' или 'warn reset'.", "desc": "Предупредить пользователя в групповом чате. Эта команда увеличивает счетчик предупреждений пользователя. Если счетчик превышает лимит, пользователь будет исключен из группы. Используйте 'warn reset' для сброса предупреждений пользователя.", "reset_usage": "Пример: warn reset @пользователь (или ответьте на сообщение пользователя и введите 'warn reset').", "cannot_remove_admin": "Я не могу удалить администратора."}, "wcg": {"usage": "Примеры:\n- wcg start (принудительно начать игру в цепочку слов)\n- wcg end (завершить текущую игру)\n- wcg <слово> (играть, продолжая цепочку слов)", "desc": "Игра в цепочку слов: Участвуйте, предоставляя слово, продолжающее цепочку. Используйте 'wcg start' для принудительного начала новой игры или 'wcg end' для завершения текущей игры."}, "wrg": {"usage": "Примеры:\n- wrg start (принудительно начать игру со случайными словами)\n- wrg end (завершить текущую игру)\n- wrg <слово> (играть, отправляя слово)", "desc": "Игра со случайными словами: Участвуйте в игре, отправляя слова в ответ на случайный запрос. Используйте 'wrg start' для начала новой игры или 'wrg end' для завершения игры."}, "yts": {"usage": "Пример: yts baymax", "desc": "Искать видео на YouTube по запросу или URL. Если предоставлен действительный URL YouTube, возвращается подробная информация о первом видео."}, "song": {"usage": "Пример: song indila love story ИЛИ song <URL YouTube> (поддерживается ответ на сообщение)", "desc": "Скачать песню с YouTube. Если предоставлен URL, песня скачивается напрямую; в противном случае выполняется поиск, и генерируется список результатов для выбора.", "not_found": "Песня по указанному запросу или URL не найдена."}, "video": {"usage": "Пример: video <URL YouTube> (или ответьте на сообщение с URL)", "desc": "Скачать видео с YouTube. Если предоставлен прямой URL, видео скачивается; если указан поисковый запрос, генерируется список результатов для выбора.", "not_found": "Видео по указанному запросу или URL не найдено."}, "update": {"usage": "Пример: update", "desc": "Проверить наличие новых обновлений. Отображает доступные обновления или подтверждает, что бот актуален.", "up_to_date": "Бот уже актуален.", "available": "{0} новых обновлений доступно:\n{1}"}, "update_now": {"usage": "Пример: update now", "desc": "Обновить бот до последней версии.", "up_to_date": "Бот уже актуален. Обновлений нет.", "updating": "Обновление бота...", "updated": "Бот успешно обновлен!"}, "antigm": {"usage": "> Usage:\n- antigm <on|off> — Enable or disable anti group mention\n- antigm <delete|warn|kick> — Set action when someone mentions the group\n- antigm ignore <jid,jid,jid,...> — Ignore anti group mention from specific groups", "action": "Action updated: group mentions will now result in *{0}*.", "filter": "Ignore JIDs updated for anti group mention.", "enabled": "Anti group mention has been enabled.", "disabled": "Anti group mention has been disabled."}}}